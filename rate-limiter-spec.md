VolleyStation API Rate Limiting — Implementation Spec

Redis-backed, token-bucket rate limiting using rate-limiter-flexible, with per-policy middleware and a single ioredis client shared by the limiter service.

A) Dependencies

Install (CI/local): npm install rate-limiter-flexible ioredis

Scope: ioredis is used only by the new RateLimiterService; no changes to any existing Redis usages.

B) Files to create

/api/services/RateLimiterService.js

Purpose: Provide a singleton ioredis client and helpers to build per-policy middleware using rate-limiter-flexible.

Functionality:

init(options):

Create one ioredis client using Redis URL/options from sails.config.connections.redis.

Base keyPrefix: sw:rl:vs.

Fast-fail configuration for resilience:

maxRetriesPerRequest: 1

enableOfflineQueue: false

connectTimeout: 500–1000ms (suggest 800ms)

Soft timeout wrapper for limiter operations:

Because the stack does not enforce per-command timeouts, wrap limiter.consume() with a soft timeout (Promise.race) using softTimeoutMs=50–100ms (suggest 75ms).

On soft timeout, treat as Redis timeout and fail-open.

Make this behavior configurable via sails.config.volleyStation.rateLimit.softTimeoutMs and sails.config.volleyStation.rateLimit.enableSoftTimeout (default enabled).

Lifecycle:

Singleton initialization guard to avoid multiple clients.

On Sails lowering, gracefully close the client (client.quit() with a short guard timeout, then client.disconnect() if needed).

createRateLimiter(policyName, policyCfg, keySelector):

Construct one RateLimiterRedis instance per policy at middleware creation time:

new RateLimiterRedis({
storeClient: ioredis,
keyPrefix: sw:rl:vs:${policyName},
points: policyCfg.points,
duration: policyCfg.duration,
blockDuration: policyCfg.blockDuration || 0,
execEvenly: true
})

Return Express/Sails-compatible middleware (req, res, next):

Resolve clientId = await keySelector(req). If empty, fail-open (next()).

Consume 1 point: limiter.consume(clientId), wrapped by the soft-timeout wrapper if enabled.

On success:

Always set headers:

RateLimit-Limit: policyCfg.points

RateLimit-Remaining: remainingPoints

RateLimit-Reset: ceil(msBeforeNext/1000)

Call next().

On rate limit exceeded:

Set headers as above, plus:

Retry-After: ceil(msBeforeNext/1000)

Respond 429 with JSON:

{ error: 'rate_limited', policy: policyName, clientId: clientIdFirst8, retryAfterMs: msBeforeNext }

Logging (info): policy, clientIdFirst8, method, path, msBeforeNext.

If blockDuration > 0, also log warn with blockDuration seconds.

On Redis error or timeout (including soft-timeout):

Log warn with context (policy, clientIdFirst8, path, error.code).

Fail-open: do not set rate-limit headers; next().

Integration: Consumed by read/write policy files (see C.2) and policy mappings (see D.2).

C) Files to modify — VolleyStation auth and policies

/api/policies/volley-station/auth.js

After token validation, compute a stable clientId:

clientId = sha256(Authorization).hex

Set req.user.clientId = clientId.

Do not log or expose raw tokens.

/api/policies/volley-station/rateLimitRead.js

Exports a single middleware created via RateLimiterService.createRateLimiter('read', sails.config.volleyStation.rateLimit.read, keySelector).

keySelector: (req) => (req.user && req.user.clientId)

Behavior:

Always set RateLimit-* headers on success and on exceed.

On exceed: 429 JSON with Retry-After.

On Redis error/timeout: warn log; fail-open with no rate-limit headers.

/api/policies/volley-station/rateLimitWrite.js

Same as rateLimitRead.js but with policyName 'write' and config sails.config.volleyStation.rateLimit.write.

D) Files to modify — Config

/config/volleyStation.js

Add per-policy limiter configs:

rateLimit: {
read:  { points: 60, duration: 60 },
write: { points: 20, duration: 60 }
}

Optional service knobs (defaults shown):

rateLimit: {
enableSoftTimeout: true,
softTimeoutMs: 75
}

/config/policies.js

Wire VolleyStation routes to the new read/write policies:

Example (adjust to your controller/action mapping):

For read (GET) routes:

['volley-station/auth', 'volley-station/rateLimitRead', 'volley-station/eventAccess']

For write (mutating) routes:

['volley-station/auth', 'volley-station/rateLimitWrite', 'volley-station/eventAccess']

Use the policy module paths relative to /api/policies.

E) Redis integration, keys, and TTL

Client library: ioredis (new, used only in /api/services/RateLimiterService.js).

Key namespace: sw:rl:vs:<policy>:<clientId> via RateLimiterRedis keyPrefix.

TTL: managed by rate-limiter-flexible (duration per policy). blockDuration (if configured) is enforced by the library.

Fault tolerance: fail-open on Redis errors/timeouts (including soft-timeout); warn logs with context.

F) 429 response and headers (policy middlewares)

Status: 429

JSON body:

{ error: 'rate_limited', policy: <policyName>, clientId: <first 8 chars>, retryAfterMs: <msBeforeNext> }

Headers (always set on success and exceed):

RateLimit-Limit: <points>

RateLimit-Remaining: <remainingPoints>

RateLimit-Reset: <ceil(msBeforeNext/1000)>

Headers (on exceed only):

Retry-After: <ceil(msBeforeNext/1000)>

G) Logging

On exceed (info): policy, clientIdFirst8, method, path, msBeforeNext.

On blockDuration (warn): policy, clientIdFirst8, path, blockDuration.

On Redis error/timeout (warn): policy, clientIdFirst8, path, error.code.

Use sails.log (or existing loggers.* if present).

H) Rollout steps

Install deps: npm install rate-limiter-flexible ioredis.

Add /api/services/RateLimiterService.js. Initialize lazily on first use; ensure singleton guard and graceful cleanup on Sails lowering.

Update /api/policies/volley-station/auth.js to attach req.user.clientId.

Add /api/policies/volley-station/rateLimitRead.js and rateLimitWrite.js using RateLimiterService.

Update /config/volleyStation.js with read/write limit configs and optional soft-timeout knobs.

Update /config/policies.js to apply rateLimitRead to read routes and rateLimitWrite to mutating routes.

Smoke test:

Call read endpoints repeatedly; observe RateLimit-* headers on successful responses and 429 after limits with Retry-After.

Call a write endpoint similarly and observe matching behavior.

