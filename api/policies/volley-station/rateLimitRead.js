const RateLimiterService = require('../../services/RateLimiterService');

// Key selector function to extract client ID from request
const keySelector = (req) => {
    return req.user && req.user.clientId;
};

// Export a function that creates the middleware at runtime
module.exports = function(req, res, next) {
    // Lazy initialization - create the middleware on first use
    if (!module.exports._middleware) {
        module.exports._middleware = RateLimiterService.createRateLimiter(
            'read',
            sails.config.volleyStation.rateLimit.read,
            keySelector
        );
    }

    // Call the actual middleware
    return module.exports._middleware(req, res, next);
};
