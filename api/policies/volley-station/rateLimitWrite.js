function keySelector(req) {
    return req.user && req.user.clientId;
}

module.exports = function(req, res, next) {
    if (!module.exports.rateLimiter) {
        module.exports.rateLimiter = RateLimiterService.createRateLimiter(
            'write',
            sails.config.volleyStation.rateLimit.read,
            keySelector
        );
    }

    return module.exports.rateLimiter(req, res, next);
};
