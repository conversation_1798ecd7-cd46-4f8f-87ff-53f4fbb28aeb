const Redis = require('ioredis');
const { RateLimiterRedis } = require('rate-limiter-flexible');

class RateLimiterService {
    constructor() {
        this._client = null;
        this._initialized = false;
    }

    _ensureInitialized() {
        if (this._initialized) {
            return;
        }

        try {
            this._initRedisClient();
            this._registerShutdownHook();
            this._initialized = true;
        } catch (e) {
            // Use console.error instead of sails.log during initialization
            console.error('RateLimiterService initialization failed:', e);
            throw e;
        }
    }

    _initRedisClient() {
        if (!sails || !sails.config || !sails.config.connections || !sails.config.connections.redis) {
            throw new Error('Sails configuration not available yet');
        }

        const redisConfig = sails.config.connections.redis;
        const clientOptions = {
            maxRetriesPerRequest: 1,
            enableOfflineQueue: false,
            connectTimeout: 800,
            lazyConnect: true,
            keyPrefix: 'sw:rl:vs:',
        };

        this._client = new Redis(redisConfig, clientOptions);
        this._client.on('ready', () => {
            // Use console.log initially, sails.log may not be available yet
            if (sails && sails.log && sails.log.verbose) {
                sails.log.verbose('RateLimiterService Redis client ready');
            } else {
                console.log('RateLimiterService Redis client ready');
            }
        });
        this._client.on('error', (error) => {
            // Use console.warn initially, sails.log may not be available yet
            if (sails && sails.log && sails.log.warn) {
                sails.log.warn('RateLimiterService Redis error:', error.code || error.message);
            } else {
                console.warn('RateLimiterService Redis error:', error.code || error.message);
            }
        });
    }

    _registerShutdownHook() {
        // Register with Sails lowering event
        if (sails && sails.on) {
            sails.on('lowering', () => this._gracefulShutdown());
        }
    }

    /**
     * Gracefully close the Redis client
     */
    async _gracefulShutdown() {
        if (!this._client) {
            return; // Nothing to shutdown
        }

        try {
            if (sails && sails.log && sails.log.verbose) {
                sails.log.verbose('RateLimiterService: Gracefully shutting down Redis client');
            } else {
                console.log('RateLimiterService: Gracefully shutting down Redis client');
            }

            // Try to quit gracefully with a timeout
            const timeoutValue = 'timeout';
            const timeoutPromise = new Promise((resolve) => {
                setTimeout(resolve, 2000, timeoutValue);
            });

            const quitPromise = this._client.quit();
            const result = await Promise.race([quitPromise, timeoutPromise]);

            if (result === timeoutValue) {
                if (sails && sails.log && sails.log.warn) {
                    sails.log.warn('RateLimiterService: Quit timeout, forcing disconnect');
                } else {
                    console.warn('RateLimiterService: Quit timeout, forcing disconnect');
                }
                this._client.disconnect();
            }
        } catch (error) {
            if (sails && sails.log && sails.log.warn) {
                sails.log.warn('RateLimiterService: Error during shutdown, forcing disconnect:', error.message);
            } else {
                console.warn('RateLimiterService: Error during shutdown, forcing disconnect:', error.message);
            }
            if (this._client) {
                this._client.disconnect();
            }
        }
    }

    /**
     * Soft timeout wrapper for limiter operations
     * @param {Promise} promise - The promise to wrap
     * @param {number} timeoutMs - Timeout in milliseconds
     */
    _withSoftTimeout(promise, timeoutMs) {
        const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => resolve({ timeout: true }), timeoutMs);
        });

        return Promise.race([promise, timeoutPromise]);
    }

    /**
     * Create rate limiter middleware
     * @param {string} rateLimiterName - Name of the rate limiter (e.g., 'read', 'write', 'api-v1')
     * @param {Object} config - Rate limiter configuration { points, duration, blockDuration, enableSoftTimeout, softTimeoutMs }
     * @param {Function} getKeySelector - Function to extract client ID from request
     * @returns {Function} Express/Sails middleware function
     */
    createRateLimiter(rateLimiterName, config, getKeySelector) {
        // Ensure Redis client is initialized before creating limiter
        this._ensureInitialized();

        const limiter = new RateLimiterRedis({
            storeClient: this._client,
            keyPrefix: `${rateLimiterName}:`,
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration || 0,
            execEvenly: true
        });

        const enableSoftTimeout = config.enableSoftTimeout !== false;
        const softTimeoutMs = config.softTimeoutMs || 75;

        // Return middleware function
        return async (req, res, next) => {
            try {
                if (req.method === 'OPTIONS') {
                    return next(); // Skip OPTIONS requests
                }

                const keySelector = await getKeySelector(req);
                if (!keySelector) {
                    // Fail-open if no key selector
                    if (sails && sails.log && sails.log.warn) {
                        sails.log.warn('RateLimiterService: No key selector, failing open');
                    } else {
                        console.warn('RateLimiterService: No key selector, failing open');
                    }
                    return next();
                }

                // Consume 1 point with an optional soft timeout
                const result = await (enableSoftTimeout
                    ? this._withSoftTimeout(limiter.consume(keySelector), softTimeoutMs)
                    : limiter.consume(keySelector)
                );

                // Handle soft timeout
                if (result && result.timeout) {
                    const logData = {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: 'soft_timeout'
                    };
                    if (sails && sails.log && sails.log.warn) {
                        sails.log.warn('RateLimiterService soft timeout:', logData);
                    } else {
                        console.warn('RateLimiterService soft timeout:', logData);
                    }
                    // Fail-open on timeout
                    return next();
                }

                // Success - set rate limit headers
                const remainingPoints = result.remainingPoints || 0;
                const msBeforeNext = result.msBeforeNext || 0;

                res.set({
                    'RateLimit-Limit': config.points,
                    'RateLimit-Remaining': remainingPoints,
                    'RateLimit-Reset': Math.ceil(msBeforeNext / 1000)
                });

                next();

            } catch (error) {
                const keySelector = await getKeySelector(req);

                if (error.remainingPoints !== undefined) {
                    // Rate limit exceeded
                    const msBeforeNext = error.msBeforeNext || 0;
                    const retryAfterSeconds = Math.ceil(msBeforeNext / 1000);

                    // Set headers
                    res.set({
                        'RateLimit-Limit': config.points,
                        'RateLimit-Remaining': 0,
                        'RateLimit-Reset': retryAfterSeconds,
                        'Retry-After': retryAfterSeconds
                    });

                    // Log rate limit exceeded
                    const exceedLogData = {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        method: req.method,
                        path: req.path,
                        retryAfterMs: msBeforeNext
                    };
                    if (sails && sails.log && sails.log.info) {
                        sails.log.info('Rate limit exceeded:', exceedLogData);
                    } else {
                        console.info('Rate limit exceeded:', exceedLogData);
                    }

                    // Log block duration if configured
                    if (config.blockDuration > 0) {
                        const blockLogData = {
                            rateLimiter: rateLimiterName,
                            keySelector,
                            path: req.path,
                            blockDuration: config.blockDuration
                        };
                        if (sails && sails.log && sails.log.warn) {
                            sails.log.warn('Rate limit block applied:', blockLogData);
                        } else {
                            console.warn('Rate limit block applied:', blockLogData);
                        }
                    }

                    // Return 429 response
                    return res.status(429).json({
                        error: 'rate_limited',
                        rateLimiter: rateLimiterName,
                        retryAfterMs: msBeforeNext
                    });
                } else {
                    // Redis error or other error - fail open
                    const errorLogData = {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: error.code || error.message
                    };
                    if (sails && sails.log && sails.log.warn) {
                        sails.log.warn('RateLimiterService error (failing open):', errorLogData);
                    } else {
                        console.warn('RateLimiterService error (failing open):', errorLogData);
                    }

                    // Fail-open: don't set rate-limit headers, just continue
                    next();
                }
            }
        };
    }
}

module.exports = new RateLimiterService();
