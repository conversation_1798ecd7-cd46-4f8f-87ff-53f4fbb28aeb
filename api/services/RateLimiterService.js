const Redis = require('ioredis');
const { RateLimiterRedis } = require('rate-limiter-flexible');

class RateLimiterService {
    constructor() {
        console.log('RATE LIMITER SERVICE CONSTRUCTOR !!!!');
        this._client = null;
    }

    init() {
        try {
            this._initRedisClient();
            this._registerShutdownHook();
        } catch (e) {
            sails.log.error('RateLimiterService initialization failed:', e);
        }
    }

    _initRedisClient() {
        const redisConfig = sails.config.connections.redis;
        const clientOptions = {
            maxRetriesPerRequest: 1,
            enableOfflineQueue: false,
            connectTimeout: 800,
            lazyConnect: true,
            keyPrefix: 'sw:rl:vs:',
        };

        this._client = new Redis(redisConfig, clientOptions);
        this._client.on('ready', () => {
            sails.log.verbose('RateLimiterService Redis client ready');
        });
        this._client.on('error', (error) => {
            sails.log.warn('RateLimiterService Redis error:', error.code || error.message);
        });
    }

    _registerShutdownHook() {
        // Register with Sails lowering event
        sails.on('lowering', () => this._gracefulShutdown());
    }

    /**
     * Gracefully close the Redis client
     */
    async _gracefulShutdown() {
        try {
            sails.log.verbose('RateLimiterService: Gracefully shutting down Redis client');
            
            // Try to quit gracefully with a timeout
            const timeoutValue = 'timeout';
            const timeoutPromise = new Promise((resolve) => {
                setTimeout(resolve, 2000, timeoutValue);
            });

            const quitPromise = this._client.quit();
            const result = await Promise.race([quitPromise, timeoutPromise]);
            
            if (result === timeoutValue) {
                sails.log.warn('RateLimiterService: Quit timeout, forcing disconnect');
                this._client.disconnect();
            }
        } catch (error) {
            sails.log.warn('RateLimiterService: Error during shutdown, forcing disconnect:', error.message);
            this._client.disconnect();
        }
    }

    /**
     * Soft timeout wrapper for limiter operations
     * @param {Promise} promise - The promise to wrap
     * @param {number} timeoutMs - Timeout in milliseconds
     */
    _withSoftTimeout(promise, timeoutMs) {
        const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => resolve({ timeout: true }), timeoutMs);
        });

        return Promise.race([promise, timeoutPromise]);
    }

    /**
     * Create rate limiter middleware
     * @param {string} rateLimiterName - Name of the rate limiter (e.g., 'read', 'write', 'api-v1')
     * @param {Object} config - Rate limiter configuration { points, duration, blockDuration, enableSoftTimeout, softTimeoutMs }
     * @param {Function} getKeySelector - Function to extract client ID from request
     * @returns {Function} Express/Sails middleware function
     */
    createRateLimiter(rateLimiterName, config, getKeySelector) {
        const limiter = new RateLimiterRedis({
            storeClient: this._client,
            keyPrefix: `${rateLimiterName}:`,
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration || 0,
            execEvenly: true
        });

        const enableSoftTimeout = config.enableSoftTimeout !== false;
        const softTimeoutMs = config.softTimeoutMs || 75;

        // Return middleware function
        return async (req, res, next) => {
            try {
                if (req.method === 'OPTIONS') {
                    return next(); // Skip OPTIONS requests
                }

                const keySelector = await getKeySelector(req);
                if (!keySelector) {
                    // Fail-open if no key selector
                    sails.log.warn('RateLimiterService: No key selector, failing open');
                    return next();
                }

                // Consume 1 point with an optional soft timeout
                const result = await (enableSoftTimeout
                    ? this._withSoftTimeout(limiter.consume(keySelector), softTimeoutMs)
                    : limiter.consume(keySelector)
                );

                // Handle soft timeout
                if (result && result.timeout) {
                    sails.log.warn('RateLimiterService soft timeout:', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: 'soft_timeout'
                    });
                    // Fail-open on timeout
                    return next();
                }

                // Success - set rate limit headers
                const remainingPoints = result.remainingPoints || 0;
                const msBeforeNext = result.msBeforeNext || 0;

                res.set({
                    'RateLimit-Limit': config.points,
                    'RateLimit-Remaining': remainingPoints,
                    'RateLimit-Reset': Math.ceil(msBeforeNext / 1000)
                });

                next();

            } catch (error) {
                const keySelector = await getKeySelector(req);

                if (error.remainingPoints !== undefined) {
                    // Rate limit exceeded
                    const msBeforeNext = error.msBeforeNext || 0;
                    const retryAfterSeconds = Math.ceil(msBeforeNext / 1000);

                    // Set headers
                    res.set({
                        'RateLimit-Limit': config.points,
                        'RateLimit-Remaining': 0,
                        'RateLimit-Reset': retryAfterSeconds,
                        'Retry-After': retryAfterSeconds
                    });

                    // Log rate limit exceeded
                    sails.log.info('Rate limit exceeded:', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        method: req.method,
                        path: req.path,
                        retryAfterMs: msBeforeNext
                    });

                    // Log block duration if configured
                    if (config.blockDuration > 0) {
                        sails.log.warn('Rate limit block applied:', {
                            rateLimiter: rateLimiterName,
                            keySelector,
                            path: req.path,
                            blockDuration: config.blockDuration
                        });
                    }

                    // Return 429 response
                    return res.status(429).json({
                        error: 'rate_limited',
                        rateLimiter: rateLimiterName,
                        retryAfterMs: msBeforeNext
                    });
                } else {
                    // Redis error or other error - fail open
                    sails.log.warn('RateLimiterService error (failing open):', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: error.code || error.message
                    });

                    // Fail-open: don't set rate-limit headers, just continue
                    next();
                }
            }
        };
    }
}

module.exports = new RateLimiterService();
