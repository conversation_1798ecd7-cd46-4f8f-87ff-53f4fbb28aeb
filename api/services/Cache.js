const redis = require('redis');
const argv = require('optimist').argv;
const swUtils = require('../lib/swUtils');
const CacheError = require('./cache/CacheError');
const Lock = require('./cache/Lock');
const CachedResult = require('./cache/Result');
const TagNameFactory = require('./cache/TagNameFactory');
const TagInvalidator = require('./cache/_TagInvalidator');
const Serializer = require('./Serializer');

swUtils.promisifyAll(redis.RedisClient.prototype);

class Cache {
    get TTL_DEFAULT() {
        return 'default';
    }

    get TTL_HOME_PAGE() {
        return 'home_page';
    }

    get TTL_SWT() {
        return 'swt';
    }

    get LOCKS_KEY() {
        return `cache:locks:${this.ENV_ID}`;
    }

    get _RESULTS_CHANNEL() {
        return `cache:results:${this.ENV_ID}`;
    }

    get _WAITING_TIMEOUT() {
        return 1000 * 15;
    }

    get instanceId() {
        const {pm_id, name} = process.env;
        if(!pm_id || !name) {
            return 'app';
        }

        return `${name}#${pm_id}`;
    }

    get ENV_ID() {
        return argv.prod
            ? 'prod'
            : 'dev';
    }

    get KEYS_MAP() {
        return {
            redis: this.TTL_DEFAULT,
            sw_home: this.TTL_HOME_PAGE,
            swt: this.TTL_SWT,
        };
    }

    get tag() {
        return TagNameFactory;
    }

    get tagInvalidator() {
        return TagInvalidator;
    }

    constructor() {
        console.log('CACHE CONSTRUCTOR !!!!');
        this._ttl = {
            [this.TTL_DEFAULT]: 300,
            [this.TTL_HOME_PAGE]: 300,
            [this.TTL_SWT]: 3600,
        };
        this._locks = new Map();
        this._client = redis.createClient(sails.config.connections.redis);
        this._subClient = redis.createClient(sails.config.connections.redis);
        this._subClient.on('message', (channel, message) => this._onChannelMessage(channel, message));
        this._subClient.subscribe(this._RESULTS_CHANNEL);
    }

    async init() {
        await Promise.all([
            this._cleanup(),
            this._loadSettings(),
            this.tagInvalidator.initGlobalInvalidation(Db),
        ]);
    }

    async getResult(
        key,
        generator,
        {
            ttl = this.TTL_DEFAULT,// Time in seconds or string id
            req = undefined,
            tags = [],
            skipCache = false,
        } = {}
    ) {
        let initialStack = {};
        Error.captureStackTrace(initialStack, this.getResult);
        initialStack = initialStack.stack;

        if (!_.isString(key)) {
            throw new CacheError('Key should be a string', {req});
        }

        if (!_.isFunction(generator)) {
            throw new CacheError('Generator should be an async function that returns result to be cached', {req});
        }

        if(skipCache) {
            return generator();
        }

        const prefixedKey = this._getPrefixedKey(key);
        let cachedResult = null;
        let isFirstInstance;
        let tagVersions;
        let lock;

        try {
            const clean = _.isObject(req) && req.query && req.query.clean === 'true';
            // after any await item might appear here
            if (!clean && this._hasLock(key)) {
                return await this._getLockResult(key, {req, tagVersions});
            }

            if (!clean) {
                cachedResult = await this._getFromCache(prefixedKey);
            }
            tagVersions = await this._getTags(tags);
            if (cachedResult) {
                if (cachedResult.isValid({tagVersions})) {
                    return cachedResult.result;
                }
            }
            if (this._hasLock(key)) {
                return await this._getLockResult(key, {req, tagVersions});
            }
            isFirstInstance = await this._acquireLock(key);
            if (isFirstInstance) {
                if(this._hasLock(key)) {
                    // Creating new Lock object in this case can cause timeout errors in high load scenarious.
                    // With this code new requests to this cached object will receive previous value
                    // until new value is generated.
                    loggers.debug_log.warn('First instance, but lock is present');
                    lock = this._locks.get(key);
                    lock.isFirst = true;
                }
                else {
                    lock = new Lock(true);
                    this._locks.set(key, lock);
                }
            }
            else {
                if(!this._hasLock(key)) {
                    this._locks.set(key, new Lock());
                }
                return await this._getLockResult(key, {req, tagVersions});
            }
        }
        catch (e) {
            if(!(e instanceof CacheError) && !swUtils.isValidationError(e)) {
                e = new CacheError('Error getting cached result', {req, prevous: e});
            }
            if(e instanceof CacheError && !e.initialStack) {
                e.initialStack = initialStack;
            }
            throw e;
        }

        let result;
        let cachingPromise;
        const deletingFromCachePromise = cachedResult && !cachedResult.isValid({tagVersions})
            ? this._client.delAsync(prefixedKey)
            : null;
        try {
            result = generator();
            if(!(result instanceof Promise)) {
                throw new CacheError('Generator function is expected to return a Promise');
            }
            result = await result;
            lock.result = result;
            await deletingFromCachePromise;
            cachingPromise = this._cacheResult(prefixedKey, {result, ttl, tagVersions});
            return result;
        }
        catch(e) {
            if(lock) {
                lock.result = e;
            }
            result = undefined;
            if(!(e instanceof CacheError) && !swUtils.isValidationError(e)) {
                e = new CacheError('Error generating result', {req, prevous: e});
            }
            if(e instanceof CacheError && !e.initialStack) {
                e.initialStack = initialStack;
            }
            throw e;
        }
        finally {
            const releaseLockPromise = isFirstInstance && this._client.hdelAsync(this.LOCKS_KEY, key);
            await cachingPromise;
            await releaseLockPromise;
            await this._publishResult(key, result);
        }
    }

    getTtl(ttl = this.TTL_DEFAULT) {
        if(Number.isInteger(ttl)) {
            return ttl;
        }
        return this._ttl[ttl] || this._ttl[this.TTL_DEFAULT];
    }

    setTtl(id, ttl) {
        if(!_.isNumber(ttl)) {
            throw new CacheError('ttl should be a number');
        }
        if(!(id in this._ttl)) {
            throw new CacheError('unknown id of ttl');
        }
        this._ttl[id] = ttl;
    }

    settingsWatcher(data) {
        if (!data || !data.key in this.KEYS_MAP) {
            return;
        }

        const settings = data.value;
        const cacheTime = settings && +settings.cache_time;

        if (cacheTime) {
            if(data.key in this.KEYS_MAP) {
                this.setTtl(this.KEYS_MAP[data.key],cacheTime);
            }
        }
    }

    async updateCacheTime(time, key = 'redis') {
        if(!time) throw new CacheError('No time specified');
        const newTime = parseInt(time, 10);
        if(!newTime || newTime < 0) throw new CacheError('Wrong time specified');
        const settingsKey = (() => {
            for(const dbKey of Object.keys(this.KEYS_MAP)) {
                if(this.KEYS_MAP[dbKey] === key) {
                    return dbKey;
                }
            }
            throw { validation: `Invalid key "${key}"` };
        })();

        const query = squel.update()
            .table('settings')
            .set(`"value" = COALESCE("value", '{}'::JSONB) || ?::JSONB`, JSON.stringify({ cache_time: newTime }))
            .where(`"key" = ?`, settingsKey);

        await Db.query(
            query
        );
    }

    async removeByMask(mask) {
        if(!mask) {
            return 0;
        }
        const keys = await this.getKeys(mask);
        if(keys.length === 0) {
            return 0;
        }
        return await this._client.delAsync(...keys);
    }

    async close() {
        await Promise.all([
            new Promise((resolve, reject) => {
                this._client.once('end', resolve);
                this._client.quit();
            }),
            new Promise((resolve, reject) => {
                this._subClient.once('end', resolve);
                this._subClient.quit();
            }),
        ]);
    }

    async getKeys(mask) {
        return this._client.keysAsync(this._getPrefixedKey(mask));
    }

    async getCachedData(key, tags = []) {
        let tagVersions = await this._getTags(tags);

        let cachedResult = await this._getFromCache(this._getPrefixedKey(key));

        if (cachedResult) {
            if (cachedResult.isValid({tagVersions})) {
                return cachedResult.result;
            }
        }
    }

    /**
     * Update value of tags
     *
     * @param {Array.<string>} tags tag names
     * @returns {Promise<undefined>}
     * @private
     */
    async invalidateTags(tags) {
        if(!_.isArray(tags)) {
            throw new Error('tags parameter should be an array of tag names');
        }
        if(tags.length === 0) {
            return;
        }
        const hashKey = this._getPrefixedTagsKey();
        let kvParams = tags.reduce((acc, val) => {
            acc[val] = this._getNewTagValue();
            return acc;
        }, {});
        await this._client.hmsetAsync(hashKey, kvParams);
    }

    _getPrefixedKey(key) {
        return `cached:${this.ENV_ID}:${key}`;
    }

    _getPrefixedTagsKey() {
        return `tags:${this.ENV_ID}`;
    }

    async _acquireLock(key) {
        return await this._client.hsetnxAsync(this.LOCKS_KEY, key, this.instanceId) === 1;
    }

    async _getFromCache(key) {
        const result = await this._client.getAsync(key);
        if(result) {
            return new CachedResult(result);
        }
        return null;
    }

    async _cacheResult(key, {result, ttl, tagVersions}) {
        await this._client.setAsync(
            key,
            CachedResult.formatForCaching({result, ttl, tagVersions}),
            'EX',
            this.getTtl(ttl)
        );
    }

    async _publishResult(key, result = undefined) {
        await this._client.publishAsync(this._RESULTS_CHANNEL, Serializer.serialize({key, result}));
    }

    async _findInstanceLocks() {
        let result = [];
        let cursor = 0;
        let data;
        do {
            [cursor, data] = await this._client.hscanAsync(this.LOCKS_KEY, cursor);
            for(const [key, value] of _.chunk(data, 2)) {
                if(value === this.instanceId) {
                    result.push(key);
                }
            }
        } while(cursor != 0);
        return result;
    }

    async _cleanup() {
        const lockedKeys = await this._findInstanceLocks();
        if(lockedKeys.length === 0) {
            return;
        }
        await this._client.hdelAsync(this.LOCKS_KEY, ...lockedKeys);
        await Promise.all(lockedKeys.map(key => this._publishResult(key)));
    }

    _onChannelMessage(channel, message) {
        if (channel === this._RESULTS_CHANNEL) {
            try {
                const {key, result = undefined} = Serializer.deserialize(message);
                const lock = this._locks.get(key);
                if (lock) {
                    if(!lock.isFirst) {
                        if (typeof result === 'undefined') {
                            lock.result = new Error('Cached result not found');
                        }
                        else {
                            lock.result = result;
                        }
                    }
                    /*
                        Deleting all locks on channel message decreases chance of
                        starting result generation on previous first instance and re-acquiring lock
                        before all locks had been released,
                        because messages to all instances are delivered at roughly the same time.
                        To make this code 100% error safe successfull delivery of message
                        to all instances needs to be awaited before deleting all locks.
                     */
                    this._locks.delete(key);
                }
            }
            catch (err) {
                loggers.errors_log.error(err);
            }
        }
    }

    async _loadSettings() {
        const {rows: settings} = await Db.query(
            squel.select()
                .from('settings')
                .fields(['key', 'value'])
                .where('key = ANY(?)', [Object.keys(this.KEYS_MAP)])
        );
        for(const {key, value:{cache_time}} of settings) {
            if(key in this.KEYS_MAP) {
                this.setTtl(this.KEYS_MAP[key], cache_time);
            }
        }
    }

    /**
     * Get current value of tags (and if not exists set default value)
     *
     * @param {Array.<string>} tags tag names
     * @returns {Promise<Object.<string, string>>} key-value collection of [tagName, tagVersion]
     * @private
     */
    async _getTags(tags) {
        if(!_.isArray(tags)) {
            throw new Error('tags parameter should be an array of tag names');
        }
        if(tags.length === 0) {
            return [];
        }
        const hashKey = this._getPrefixedTagsKey();
        let result = {};
        for(const name of tags) {
            await this._client.hsetnxAsync(hashKey, name, this._getNewTagValue());
        }
        const values = await this._client.hmgetAsync(hashKey, tags);
        return values.reduce((acc, val, idx) => {
            acc[tags[idx]] = val;
            return acc;
        }, {});
    }

    _getNewTagValue() {
        return Math.random().toString(36).substring(2);
    }

    _hasLock(key) {
        return this._locks.has(key);
    }

    async _getLockResult(key, {req, tagVersions}) {
        const lock = this._locks.get(key);
        if(!lock) {
            throw new CacheError('Trying to wait for not existing lock', {req});
        }
        if(!lock.timeout) {
            lock.timeout = setTimeout(
                async () => {
                    lock.timeout = undefined;
                    // redis pubsub doesn't guarantee to deliver all messages.
                    // In case message was lost this code will try to return successfully cached result
                    // before throwing error
                    const cachedResult = await this._getFromCache(this._getPrefixedKey(key));
                    if (cachedResult && cachedResult.isValid({tagVersions})) {
                        loggers.debug_log.warn(`Instance didn't receive result message. Result returned found from cache.`);
                        lock.result = cachedResult.result;
                    }
                    else {
                        lock.result = Error('Cached result querying timed out');
                    }
                    if(this._locks.get(key) === lock) {
                        this._locks.delete(key);
                    }
                    else {
                        loggers.debug_log.warn(`Lock object was reassigned`);
                    }
                },
                this._WAITING_TIMEOUT
            );
        }
        return (async () => {
            try {
                return await lock.getResult();
            }
            finally {
                if(lock.timeout) {
                    clearTimeout(lock.timeout);
                    lock.timeout = undefined;
                }
            }
        })();
    }
}

module.exports = new Cache();
