(function(){function e(){e.history=e.history||[],e.history.push(arguments),this.console&&console.log(Array.prototype.slice.call(arguments)[0])}this.Handlebars=this.Handlebars||{},this.Handlebars.templates=this.Handlebars.templates||{},this.Handlebars.templates.apikey_auth=Handlebars.template({1:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'                <span class="key_auth__value">'+s((i=null!=(i=t.value||(null!=e?e.value:e))?i:o,typeof i===a?i.call(e,{name:"value",hash:{},data:r}):i))+"</span>\n"},3:function(e,t,n,r){return'                <input placeholder="api_key" class="auth_input input_apiKey_entry" name="apiKey" type="text"/>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='<div class="key_input_container">\n    <h3 class="auth__title">Api key authorization</h3>\n    <div class="auth__description">'+l((a=null!=(a=t.description||(null!=e?e.description:e))?a:s,typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a))+'</div>\n    <div>\n        <div class="key_auth__field">\n            <span class="key_auth__label">name:</span>\n            <span class="key_auth__value">'+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+'</span>\n        </div>\n        <div class="key_auth__field">\n            <span class="key_auth__label">in:</span>\n            <span class="key_auth__value">'+l((a=null!=(a=t["in"]||(null!=e?e["in"]:e))?a:s,typeof a===o?a.call(e,{name:"in",hash:{},data:r}):a))+'</span>\n        </div>\n        <div class="key_auth__field">\n            <span class="key_auth__label">value:</span>\n';return i=t["if"].call(e,null!=e?e.isLogout:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(3,r),data:r}),null!=i&&(u+=i),u+"        </div>\n    </div>\n</div>\n"},useData:!0}),this.Handlebars.templates.auth_button_operation=Handlebars.template({1:function(e,t,n,r){return"        authorize__btn_operation_login\n"},3:function(e,t,n,r){return"        authorize__btn_operation_logout\n"},5:function(e,t,n,r){var i,a='        <ul class="authorize-scopes">\n';return i=t.each.call(e,null!=e?e.scopes:e,{name:"each",hash:{},fn:this.program(6,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a+"        </ul>\n"},6:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'                <li class="authorize__scope" title="'+s((i=null!=(i=t.description||(null!=e?e.description:e))?i:o,typeof i===a?i.call(e,{name:"description",hash:{},data:r}):i))+'">'+s((i=null!=(i=t.scope||(null!=e?e.scope:e))?i:o,typeof i===a?i.call(e,{name:"scope",hash:{},data:r}):i))+"</li>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a='<div class="authorize__btn authorize__btn_operation\n';return i=t["if"].call(e,null!=e?e.isLogout:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(3,r),data:r}),null!=i&&(a+=i),a+='">\n',i=t["if"].call(e,null!=e?e.scopes:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a+"</div>\n"},useData:!0}),this.Handlebars.templates.auth_button=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){return"<a class='authorize__btn' href=\"#\">Authorize</a>\n"},useData:!0}),this.Handlebars.templates.auth_view=Handlebars.template({1:function(e,t,n,r){return'            <button type="button" class="auth__button auth_submit__button" data-sw-translate>Authorize</button>\n'},3:function(e,t,n,r){return'            <button type="button" class="auth__button auth_logout__button" data-sw-translate>Logout</button>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a='<div class="auth_container">\n\n    <div class="auth_inner"></div>\n    <div class="auth_submit">\n';return i=t.unless.call(e,null!=e?e.isLogout:e,{name:"unless",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(a+=i),i=t["if"].call(e,null!=e?e.isAuthorized:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a+"    </div>\n\n</div>\n"},useData:!0}),this.Handlebars.templates.basic_auth=Handlebars.template({1:function(e,t,n,r){return" - authorized"},3:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'                <span class="basic_auth__value">'+s((i=null!=(i=t.username||(null!=e?e.username:e))?i:o,typeof i===a?i.call(e,{name:"username",hash:{},data:r}):i))+"</span>\n"},5:function(e,t,n,r){return'                <input required placeholder="username" class="basic_auth__username auth_input" name="username" type="text"/>\n'},7:function(e,t,n,r){return'            <div class="auth_label">\n                <span class="basic_auth__label" data-sw-translate>password:</span>\n                <input required placeholder="password" class="basic_auth__password auth_input" name="password" type="password"/></label>\n            </div>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<div class='basic_auth_container'>\n    <h3 class=\"auth__title\">Basic authentication";return i=t["if"].call(e,null!=e?e.isLogout:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+='</h3>\n    <form class="basic_input_container">\n        <div class="auth__description">'+l((a=null!=(a=t.description||(null!=e?e.description:e))?a:s,typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a))+'</div>\n        <div class="auth_label">\n            <span class="basic_auth__label" data-sw-translate>username:</span>\n',i=t["if"].call(e,null!=e?e.isLogout:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.program(5,r),data:r}),null!=i&&(u+=i),u+="        </div>\n",i=t.unless.call(e,null!=e?e.isLogout:e,{name:"unless",hash:{},fn:this.program(7,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"    </form>\n</div>\n"},useData:!0}),this.Handlebars.templates.content_type=Handlebars.template({1:function(e,t,n,r){var i,a="";return i=t.each.call(e,null!=e?e.produces:e,{name:"each",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a},2:function(e,t,n,r){var i=this.lambda,a=this.escapeExpression;return'	<option value="'+a(i(e,e))+'">'+a(i(e,e))+"</option>\n"},4:function(e,t,n,r){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='<label data-sw-translate for="'+l((a=null!=(a=t.contentTypeId||(null!=e?e.contentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"contentTypeId",hash:{},data:r}):a))+'">Response Content Type</label>\n<select name="contentType" id="'+l((a=null!=(a=t.contentTypeId||(null!=e?e.contentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"contentTypeId",hash:{},data:r}):a))+'">\n';return i=t["if"].call(e,null!=e?e.produces:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(4,r),data:r}),null!=i&&(u+=i),u+"</select>\n"},useData:!0}),$(function(){$.fn.vAlign=function(){return this.each(function(){var e=$(this).height(),t=$(this).parent().height(),n=(t-e)/2;$(this).css("margin-top",n)})},$.fn.stretchFormtasticInputWidthToParent=function(){return this.each(function(){var e=$(this).closest("form").innerWidth(),t=parseInt($(this).closest("form").css("padding-left"),10)+parseInt($(this).closest("form").css("padding-right"),10),n=parseInt($(this).css("padding-left"),10)+parseInt($(this).css("padding-right"),10);$(this).css("width",e-t-n)})},$("form.formtastic li.string input, form.formtastic textarea").stretchFormtasticInputWidthToParent(),$("ul.downplayed li div.content p").vAlign(),$("form.sandbox").submit(function(){var e=!0;return $(this).find("input.required").each(function(){$(this).removeClass("error"),""===$(this).val()&&($(this).addClass("error"),$(this).wiggle(),e=!1)}),e})}),Function.prototype.bind&&console&&"object"==typeof console.log&&["log","info","warn","error","assert","dir","clear","profile","profileEnd"].forEach(function(e){console[e]=this.bind(console[e],console)},Function.prototype.call),window.Docs={shebang:function(){var e=$.param.fragment().split("/");switch(e.shift(),e.length){case 1:if(e[0].length>0){var t="resource_"+e[0];Docs.expandEndpointListForResource(e[0]),$("#"+t).slideto({highlight:!1})}break;case 2:Docs.expandEndpointListForResource(e[0]),$("#"+t).slideto({highlight:!1});var n=e.join("_"),r=n+"_content";Docs.expandOperation($("#"+r)),$("#"+n).slideto({highlight:!1})}},toggleEndpointListForResource:function(e){var t=$("li#resource_"+Docs.escapeResourceName(e)+" ul.endpoints");t.is(":visible")?($.bbq.pushState("#/",2),Docs.collapseEndpointListForResource(e)):($.bbq.pushState("#/"+e,2),Docs.expandEndpointListForResource(e))},expandEndpointListForResource:function(e){var e=Docs.escapeResourceName(e);if(""==e)return void $(".resource ul.endpoints").slideDown();$("li#resource_"+e).addClass("active");var t=$("li#resource_"+e+" ul.endpoints");t.slideDown()},collapseEndpointListForResource:function(e){var e=Docs.escapeResourceName(e);if(""==e)return void $(".resource ul.endpoints").slideUp();$("li#resource_"+e).removeClass("active");var t=$("li#resource_"+e+" ul.endpoints");t.slideUp()},expandOperationsForResource:function(e){return Docs.expandEndpointListForResource(e),""==e?void $(".resource ul.endpoints li.operation div.content").slideDown():void $("li#resource_"+Docs.escapeResourceName(e)+" li.operation div.content").each(function(){Docs.expandOperation($(this))})},collapseOperationsForResource:function(e){return Docs.expandEndpointListForResource(e),""==e?void $(".resource ul.endpoints li.operation div.content").slideUp():void $("li#resource_"+Docs.escapeResourceName(e)+" li.operation div.content").each(function(){Docs.collapseOperation($(this))})},escapeResourceName:function(e){return e.replace(/[!"#$%&'()*+,.\/:;<=>?@\[\\\]\^`{|}~]/g,"\\$&")},expandOperation:function(e){e.slideDown()},collapseOperation:function(e){e.slideUp()}},function(e,t){"use strict";"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.returnExports=t()}(this,function(){var e,t,n=Array,r=n.prototype,i=Object,a=i.prototype,o=Function,s=o.prototype,l=String,u=l.prototype,c=Number,p=c.prototype,h=r.slice,f=r.splice,d=r.push,m=r.unshift,y=r.concat,g=r.join,v=s.call,b=s.apply,w=Math.max,x=Math.min,j=a.toString,O="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,A=Function.prototype.toString,S=/^\s*class /,_=function(e){try{var t=A.call(e),n=t.replace(/\/\/.*\n/g,""),r=n.replace(/\/\*[.\s\S]*\*\//g,""),i=r.replace(/\n/gm," ").replace(/ {2}/g," ");return S.test(i)}catch(a){return!1}},k=function(e){try{return _(e)?!1:(A.call(e),!0)}catch(t){return!1}},E="[object Function]",C="[object GeneratorFunction]",e=function(e){if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(O)return k(e);if(_(e))return!1;var t=j.call(e);return t===E||t===C},I=RegExp.prototype.exec,T=function(e){try{return I.call(e),!0}catch(t){return!1}},M="[object RegExp]";t=function(e){return"object"!=typeof e?!1:O?T(e):j.call(e)===M};var $,U=String.prototype.valueOf,D=function(e){try{return U.call(e),!0}catch(t){return!1}},P="[object String]";$=function(e){return"string"==typeof e?!0:"object"!=typeof e?!1:O?D(e):j.call(e)===P};var L=i.defineProperty&&function(){try{var e={};i.defineProperty(e,"x",{enumerable:!1,value:e});for(var t in e)return!1;return e.x===e}catch(n){return!1}}(),R=function(e){var t;return t=L?function(e,t,n,r){!r&&t in e||i.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(e,t,n,r){!r&&t in e||(e[t]=n)},function(n,r,i){for(var a in r)e.call(r,a)&&t(n,a,r[a],i)}}(a.hasOwnProperty),N=function(e){var t=typeof e;return null===e||"object"!==t&&"function"!==t},F=c.isNaN||function(e){return e!==e},B={ToInteger:function(e){var t=+e;return F(t)?t=0:0!==t&&t!==1/0&&t!==-(1/0)&&(t=(t>0||-1)*Math.floor(Math.abs(t))),t},ToPrimitive:function(t){var n,r,i;if(N(t))return t;if(r=t.valueOf,e(r)&&(n=r.call(t),N(n)))return n;if(i=t.toString,e(i)&&(n=i.call(t),N(n)))return n;throw new TypeError},ToObject:function(e){if(null==e)throw new TypeError("can't convert "+e+" to object");return i(e)},ToUint32:function(e){return e>>>0}},q=function(){};R(s,{bind:function(t){var n=this;if(!e(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var r,a=h.call(arguments,1),s=function(){if(this instanceof r){var e=b.call(n,this,y.call(a,h.call(arguments)));return i(e)===e?e:this}return b.call(n,t,y.call(a,h.call(arguments)))},l=w(0,n.length-a.length),u=[],c=0;l>c;c++)d.call(u,"$"+c);return r=o("binder","return function ("+g.call(u,",")+"){ return binder.apply(this, arguments); }")(s),n.prototype&&(q.prototype=n.prototype,r.prototype=new q,q.prototype=null),r}});var V=v.bind(a.hasOwnProperty),z=v.bind(a.toString),H=v.bind(h),J=b.bind(h),Y=v.bind(u.slice),Q=v.bind(u.split),K=v.bind(u.indexOf),W=v.bind(d),X=v.bind(a.propertyIsEnumerable),G=v.bind(r.sort),Z=n.isArray||function(e){return"[object Array]"===z(e)},ee=1!==[].unshift(0);R(r,{unshift:function(){return m.apply(this,arguments),this.length}},ee),R(n,{isArray:Z});var te=i("a"),ne="a"!==te[0]||!(0 in te),re=function(e){var t=!0,n=!0,r=!1;if(e)try{e.call("foo",function(e,n,r){"object"!=typeof r&&(t=!1)}),e.call([1],function(){"use strict";n="string"==typeof this},"x")}catch(i){r=!0}return!!e&&!r&&t&&n};R(r,{forEach:function(t){var n,r=B.ToObject(this),i=ne&&$(this)?Q(this,""):r,a=-1,o=B.ToUint32(i.length);if(arguments.length>1&&(n=arguments[1]),!e(t))throw new TypeError("Array.prototype.forEach callback must be a function");for(;++a<o;)a in i&&("undefined"==typeof n?t(i[a],a,r):t.call(n,i[a],a,r))}},!re(r.forEach)),R(r,{map:function(t){var r,i=B.ToObject(this),a=ne&&$(this)?Q(this,""):i,o=B.ToUint32(a.length),s=n(o);if(arguments.length>1&&(r=arguments[1]),!e(t))throw new TypeError("Array.prototype.map callback must be a function");for(var l=0;o>l;l++)l in a&&("undefined"==typeof r?s[l]=t(a[l],l,i):s[l]=t.call(r,a[l],l,i));return s}},!re(r.map)),R(r,{filter:function(t){var n,r,i=B.ToObject(this),a=ne&&$(this)?Q(this,""):i,o=B.ToUint32(a.length),s=[];if(arguments.length>1&&(r=arguments[1]),!e(t))throw new TypeError("Array.prototype.filter callback must be a function");for(var l=0;o>l;l++)l in a&&(n=a[l],("undefined"==typeof r?t(n,l,i):t.call(r,n,l,i))&&W(s,n));return s}},!re(r.filter)),R(r,{every:function(t){var n,r=B.ToObject(this),i=ne&&$(this)?Q(this,""):r,a=B.ToUint32(i.length);if(arguments.length>1&&(n=arguments[1]),!e(t))throw new TypeError("Array.prototype.every callback must be a function");for(var o=0;a>o;o++)if(o in i&&!("undefined"==typeof n?t(i[o],o,r):t.call(n,i[o],o,r)))return!1;return!0}},!re(r.every)),R(r,{some:function(t){var n,r=B.ToObject(this),i=ne&&$(this)?Q(this,""):r,a=B.ToUint32(i.length);if(arguments.length>1&&(n=arguments[1]),!e(t))throw new TypeError("Array.prototype.some callback must be a function");for(var o=0;a>o;o++)if(o in i&&("undefined"==typeof n?t(i[o],o,r):t.call(n,i[o],o,r)))return!0;return!1}},!re(r.some));var ie=!1;r.reduce&&(ie="object"==typeof r.reduce.call("es5",function(e,t,n,r){return r})),R(r,{reduce:function(t){var n=B.ToObject(this),r=ne&&$(this)?Q(this,""):n,i=B.ToUint32(r.length);if(!e(t))throw new TypeError("Array.prototype.reduce callback must be a function");if(0===i&&1===arguments.length)throw new TypeError("reduce of empty array with no initial value");var a,o=0;if(arguments.length>=2)a=arguments[1];else for(;;){if(o in r){a=r[o++];break}if(++o>=i)throw new TypeError("reduce of empty array with no initial value")}for(;i>o;o++)o in r&&(a=t(a,r[o],o,n));return a}},!ie);var ae=!1;r.reduceRight&&(ae="object"==typeof r.reduceRight.call("es5",function(e,t,n,r){return r})),R(r,{reduceRight:function(t){var n=B.ToObject(this),r=ne&&$(this)?Q(this,""):n,i=B.ToUint32(r.length);if(!e(t))throw new TypeError("Array.prototype.reduceRight callback must be a function");if(0===i&&1===arguments.length)throw new TypeError("reduceRight of empty array with no initial value");var a,o=i-1;if(arguments.length>=2)a=arguments[1];else for(;;){if(o in r){a=r[o--];break}if(--o<0)throw new TypeError("reduceRight of empty array with no initial value")}if(0>o)return a;do o in r&&(a=t(a,r[o],o,n));while(o--);return a}},!ae);var oe=r.indexOf&&-1!==[0,1].indexOf(1,2);R(r,{indexOf:function(e){var t=ne&&$(this)?Q(this,""):B.ToObject(this),n=B.ToUint32(t.length);if(0===n)return-1;var r=0;for(arguments.length>1&&(r=B.ToInteger(arguments[1])),r=r>=0?r:w(0,n+r);n>r;r++)if(r in t&&t[r]===e)return r;return-1}},oe);var se=r.lastIndexOf&&-1!==[0,1].lastIndexOf(0,-3);R(r,{lastIndexOf:function(e){var t=ne&&$(this)?Q(this,""):B.ToObject(this),n=B.ToUint32(t.length);if(0===n)return-1;var r=n-1;for(arguments.length>1&&(r=x(r,B.ToInteger(arguments[1]))),r=r>=0?r:n-Math.abs(r);r>=0;r--)if(r in t&&e===t[r])return r;return-1}},se);var le=function(){var e=[1,2],t=e.splice();return 2===e.length&&Z(t)&&0===t.length}();R(r,{splice:function(e,t){return 0===arguments.length?[]:f.apply(this,arguments)}},!le);var ue=function(){var e={};return r.splice.call(e,0,0,1),1===e.length}();R(r,{splice:function(e,t){if(0===arguments.length)return[];var n=arguments;return this.length=w(B.ToInteger(this.length),0),arguments.length>0&&"number"!=typeof t&&(n=H(arguments),n.length<2?W(n,this.length-e):n[1]=B.ToInteger(t)),f.apply(this,n)}},!ue);var ce=function(){var e=new n(1e5);return e[8]="x",e.splice(1,1),7===e.indexOf("x")}(),pe=function(){var e=256,t=[];return t[e]="a",t.splice(e+1,0,"b"),"a"===t[e]}();R(r,{splice:function(e,t){for(var n,r=B.ToObject(this),i=[],a=B.ToUint32(r.length),o=B.ToInteger(e),s=0>o?w(a+o,0):x(o,a),u=x(w(B.ToInteger(t),0),a-s),c=0;u>c;)n=l(s+c),V(r,n)&&(i[c]=r[n]),c+=1;var p,h=H(arguments,2),f=h.length;if(u>f){c=s;for(var d=a-u;d>c;)n=l(c+u),p=l(c+f),V(r,n)?r[p]=r[n]:delete r[p],c+=1;c=a;for(var m=a-u+f;c>m;)delete r[c-1],c-=1}else if(f>u)for(c=a-u;c>s;)n=l(c+u-1),p=l(c+f-1),V(r,n)?r[p]=r[n]:delete r[p],c-=1;c=s;for(var y=0;y<h.length;++y)r[c]=h[y],c+=1;return r.length=a-u+f,i}},!ce||!pe);var he,fe=r.join;try{he="1,2,3"!==Array.prototype.join.call("123",",")}catch(de){he=!0}he&&R(r,{join:function(e){var t="undefined"==typeof e?",":e;return fe.call($(this)?Q(this,""):this,t)}},he);var me="1,2"!==[1,2].join(void 0);me&&R(r,{join:function(e){var t="undefined"==typeof e?",":e;return fe.call(this,t)}},me);var ye=function(e){for(var t=B.ToObject(this),n=B.ToUint32(t.length),r=0;r<arguments.length;)t[n+r]=arguments[r],r+=1;return t.length=n+r,n+r},ge=function(){var e={},t=Array.prototype.push.call(e,void 0);return 1!==t||1!==e.length||"undefined"!=typeof e[0]||!V(e,0)}();R(r,{push:function(e){return Z(this)?d.apply(this,arguments):ye.apply(this,arguments)}},ge);var ve=function(){var e=[],t=e.push(void 0);return 1!==t||1!==e.length||"undefined"!=typeof e[0]||!V(e,0)}();R(r,{push:ye},ve),R(r,{slice:function(e,t){var n=$(this)?Q(this,""):this;return J(n,arguments)}},ne);var be=function(){try{return[1,2].sort(null),[1,2].sort({}),!0}catch(e){}return!1}(),we=function(){try{return[1,2].sort(/a/),!1}catch(e){}return!0}(),xe=function(){try{return[1,2].sort(void 0),!0}catch(e){}return!1}();R(r,{sort:function(t){if("undefined"==typeof t)return G(this);if(!e(t))throw new TypeError("Array.prototype.sort callback must be a function");return G(this,t)}},be||!xe||!we);var je=!X({toString:null},"toString"),Oe=X(function(){},"prototype"),Ae=!V("x","0"),Se=function(e){var t=e.constructor;return t&&t.prototype===e},_e={$window:!0,$console:!0,$parent:!0,$self:!0,$frame:!0,$frames:!0,$frameElement:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$external:!0},ke=function(){if("undefined"==typeof window)return!1;for(var e in window)try{!_e["$"+e]&&V(window,e)&&null!==window[e]&&"object"==typeof window[e]&&Se(window[e])}catch(t){return!0}return!1}(),Ee=function(e){if("undefined"==typeof window||!ke)return Se(e);try{return Se(e)}catch(t){return!1}},Ce=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],Ie=Ce.length,Te=function(e){return"[object Arguments]"===z(e)},Me=function(t){return null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&!Z(t)&&e(t.callee)},$e=Te(arguments)?Te:Me;R(i,{keys:function(t){var n=e(t),r=$e(t),i=null!==t&&"object"==typeof t,a=i&&$(t);if(!i&&!n&&!r)throw new TypeError("Object.keys called on a non-object");var o=[],s=Oe&&n;if(a&&Ae||r)for(var u=0;u<t.length;++u)W(o,l(u));if(!r)for(var c in t)s&&"prototype"===c||!V(t,c)||W(o,l(c));if(je)for(var p=Ee(t),h=0;Ie>h;h++){var f=Ce[h];p&&"constructor"===f||!V(t,f)||W(o,f)}return o}});var Ue=i.keys&&function(){return 2===i.keys(arguments).length}(1,2),De=i.keys&&function(){var e=i.keys(arguments);return 1!==arguments.length||1!==e.length||1!==e[0]}(1),Pe=i.keys;R(i,{keys:function(e){return Pe($e(e)?H(e):e)}},!Ue||De);var Le,Re,Ne=0!==new Date(-0xc782b5b342b24).getUTCMonth(),Fe=new Date(-0x55d318d56a724),Be=new Date(14496624e5),qe="Mon, 01 Jan -45875 11:59:59 GMT"!==Fe.toUTCString(),Ve=Fe.getTimezoneOffset();-720>Ve?(Le="Tue Jan 02 -45875"!==Fe.toDateString(),Re=!/^Thu Dec 10 2015 \d\d:\d\d:\d\d GMT[-\+]\d\d\d\d(?: |$)/.test(Be.toString())):(Le="Mon Jan 01 -45875"!==Fe.toDateString(),Re=!/^Wed Dec 09 2015 \d\d:\d\d:\d\d GMT[-\+]\d\d\d\d(?: |$)/.test(Be.toString()));var ze=v.bind(Date.prototype.getFullYear),He=v.bind(Date.prototype.getMonth),Je=v.bind(Date.prototype.getDate),Ye=v.bind(Date.prototype.getUTCFullYear),Qe=v.bind(Date.prototype.getUTCMonth),Ke=v.bind(Date.prototype.getUTCDate),We=v.bind(Date.prototype.getUTCDay),Xe=v.bind(Date.prototype.getUTCHours),Ge=v.bind(Date.prototype.getUTCMinutes),Ze=v.bind(Date.prototype.getUTCSeconds),et=v.bind(Date.prototype.getUTCMilliseconds),tt=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],nt=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],rt=function(e,t){return Je(new Date(t,e,0))};R(Date.prototype,{getFullYear:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=ze(this);return 0>e&&He(this)>11?e+1:e},getMonth:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=ze(this),t=He(this);return 0>e&&t>11?0:t},getDate:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=ze(this),t=He(this),n=Je(this);if(0>e&&t>11){if(12===t)return n;var r=rt(0,e+1);return r-n+1}return n},getUTCFullYear:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ye(this);return 0>e&&Qe(this)>11?e+1:e},getUTCMonth:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ye(this),t=Qe(this);return 0>e&&t>11?0:t},getUTCDate:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ye(this),t=Qe(this),n=Ke(this);if(0>e&&t>11){if(12===t)return n;var r=rt(0,e+1);return r-n+1}return n}},Ne),R(Date.prototype,{toUTCString:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=We(this),t=Ke(this),n=Qe(this),r=Ye(this),i=Xe(this),a=Ge(this),o=Ze(this);return tt[e]+", "+(10>t?"0"+t:t)+" "+nt[n]+" "+r+" "+(10>i?"0"+i:i)+":"+(10>a?"0"+a:a)+":"+(10>o?"0"+o:o)+" GMT"}},Ne||qe),R(Date.prototype,{toDateString:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=this.getDay(),t=this.getDate(),n=this.getMonth(),r=this.getFullYear();return tt[e]+" "+nt[n]+" "+(10>t?"0"+t:t)+" "+r}},Ne||Le),(Ne||Re)&&(Date.prototype.toString=function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=this.getDay(),t=this.getDate(),n=this.getMonth(),r=this.getFullYear(),i=this.getHours(),a=this.getMinutes(),o=this.getSeconds(),s=this.getTimezoneOffset(),l=Math.floor(Math.abs(s)/60),u=Math.floor(Math.abs(s)%60);return tt[e]+" "+nt[n]+" "+(10>t?"0"+t:t)+" "+r+" "+(10>i?"0"+i:i)+":"+(10>a?"0"+a:a)+":"+(10>o?"0"+o:o)+" GMT"+(s>0?"-":"+")+(10>l?"0"+l:l)+(10>u?"0"+u:u)},L&&i.defineProperty(Date.prototype,"toString",{configurable:!0,enumerable:!1,writable:!0}));var it=-621987552e5,at="-000001",ot=Date.prototype.toISOString&&-1===new Date(it).toISOString().indexOf(at),st=Date.prototype.toISOString&&"1969-12-31T23:59:59.999Z"!==new Date(-1).toISOString(),lt=v.bind(Date.prototype.getTime);R(Date.prototype,{toISOString:function(){if(!isFinite(this)||!isFinite(lt(this)))throw new RangeError("Date.prototype.toISOString called on non-finite value.");var e=Ye(this),t=Qe(this);e+=Math.floor(t/12),t=(t%12+12)%12;var n=[t+1,Ke(this),Xe(this),Ge(this),Ze(this)];e=(0>e?"-":e>9999?"+":"")+Y("00000"+Math.abs(e),e>=0&&9999>=e?-4:-6);for(var r=0;r<n.length;++r)n[r]=Y("00"+n[r],-2);return e+"-"+H(n,0,2).join("-")+"T"+H(n,2).join(":")+"."+Y("000"+et(this),-3)+"Z"}},ot||st);var ut=function(){try{return Date.prototype.toJSON&&null===new Date(NaN).toJSON()&&-1!==new Date(it).toJSON().indexOf(at)&&Date.prototype.toJSON.call({toISOString:function(){return!0}})}catch(e){return!1}}();ut||(Date.prototype.toJSON=function(t){var n=i(this),r=B.ToPrimitive(n);if("number"==typeof r&&!isFinite(r))return null;var a=n.toISOString;if(!e(a))throw new TypeError("toISOString property is not callable");return a.call(n)});var ct=1e15===Date.parse("+033658-09-27T01:46:40.000Z"),pt=!isNaN(Date.parse("2012-04-04T24:00:00.500Z"))||!isNaN(Date.parse("2012-11-31T23:59:59.000Z"))||!isNaN(Date.parse("2012-12-31T23:59:60.000Z")),ht=isNaN(Date.parse("2000-01-01T00:00:00.000Z"));if(ht||pt||!ct){var ft=Math.pow(2,31)-1,dt=F(new Date(1970,0,1,0,0,0,ft+1).getTime());Date=function(e){var t=function(n,r,i,a,o,s,u){var c,p=arguments.length;if(this instanceof e){var h=s,f=u;if(dt&&p>=7&&u>ft){var d=Math.floor(u/ft)*ft,m=Math.floor(d/1e3);h+=m,f-=1e3*m}c=1===p&&l(n)===n?new e(t.parse(n)):p>=7?new e(n,r,i,a,o,h,f):p>=6?new e(n,r,i,a,o,h):p>=5?new e(n,r,i,a,o):p>=4?new e(n,r,i,a):p>=3?new e(n,r,i):p>=2?new e(n,r):p>=1?new e(n instanceof e?+n:n):new e}else c=e.apply(this,arguments);return N(c)||R(c,{constructor:t},!0),c},n=new RegExp("^(\\d{4}|[+-]\\d{6})(?:-(\\d{2})(?:-(\\d{2})(?:T(\\d{2}):(\\d{2})(?::(\\d{2})(?:(\\.\\d{1,}))?)?(Z|(?:([-+])(\\d{2}):(\\d{2})))?)?)?)?$"),r=[0,31,59,90,120,151,181,212,243,273,304,334,365],i=function(e,t){var n=t>1?1:0;return r[t]+Math.floor((e-1969+n)/4)-Math.floor((e-1901+n)/100)+Math.floor((e-1601+n)/400)+365*(e-1970)},a=function(t){var n=0,r=t;if(dt&&r>ft){var i=Math.floor(r/ft)*ft,a=Math.floor(i/1e3);n+=a,r-=1e3*a}return c(new e(1970,0,1,0,0,n,r))};for(var o in e)V(e,o)&&(t[o]=e[o]);R(t,{now:e.now,UTC:e.UTC},!0),t.prototype=e.prototype,R(t.prototype,{constructor:t},!0);var s=function(t){var r=n.exec(t);if(r){var o,s=c(r[1]),l=c(r[2]||1)-1,u=c(r[3]||1)-1,p=c(r[4]||0),h=c(r[5]||0),f=c(r[6]||0),d=Math.floor(1e3*c(r[7]||0)),m=Boolean(r[4]&&!r[8]),y="-"===r[9]?1:-1,g=c(r[10]||0),v=c(r[11]||0),b=h>0||f>0||d>0;return(b?24:25)>p&&60>h&&60>f&&1e3>d&&l>-1&&12>l&&24>g&&60>v&&u>-1&&u<i(s,l+1)-i(s,l)&&(o=60*(24*(i(s,l)+u)+p+g*y),o=1e3*(60*(o+h+v*y)+f)+d,m&&(o=a(o)),o>=-864e13&&864e13>=o)?o:NaN}return e.parse.apply(this,arguments)};return R(t,{parse:s}),t}(Date)}Date.now||(Date.now=function(){return(new Date).getTime()});var mt=p.toFixed&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==0xde0b6b3a7640080.toFixed(0)),yt={base:1e7,size:6,data:[0,0,0,0,0,0],multiply:function(e,t){for(var n=-1,r=t;++n<yt.size;)r+=e*yt.data[n],yt.data[n]=r%yt.base,r=Math.floor(r/yt.base)},divide:function(e){for(var t=yt.size,n=0;--t>=0;)n+=yt.data[t],yt.data[t]=Math.floor(n/e),n=n%e*yt.base},numToString:function(){for(var e=yt.size,t="";--e>=0;)if(""!==t||0===e||0!==yt.data[e]){var n=l(yt.data[e]);""===t?t=n:t+=Y("0000000",0,7-n.length)+n}return t},pow:function Pt(e,t,n){return 0===t?n:t%2===1?Pt(e,t-1,n*e):Pt(e*e,t/2,n)},log:function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}},gt=function(e){var t,n,r,i,a,o,s,u;if(t=c(e),t=F(t)?0:Math.floor(t),0>t||t>20)throw new RangeError("Number.toFixed called with invalid number of decimals");if(n=c(this),F(n))return"NaN";if(-1e21>=n||n>=1e21)return l(n);if(r="",0>n&&(r="-",n=-n),i="0",n>1e-21)if(a=yt.log(n*yt.pow(2,69,1))-69,o=0>a?n*yt.pow(2,-a,1):n/yt.pow(2,a,1),o*=4503599627370496,a=52-a,a>0){for(yt.multiply(0,o),s=t;s>=7;)yt.multiply(1e7,0),s-=7;for(yt.multiply(yt.pow(10,s,1),0),s=a-1;s>=23;)yt.divide(1<<23),s-=23;yt.divide(1<<s),yt.multiply(1,1),yt.divide(2),i=yt.numToString()}else yt.multiply(0,o),yt.multiply(1<<-a,0),i=yt.numToString()+Y("0.00000000000000000000",2,2+t);return t>0?(u=i.length,i=t>=u?r+Y("0.0000000000000000000",0,t-u+2)+i:r+Y(i,0,u-t)+"."+Y(i,u-t)):i=r+i,i};R(p,{toFixed:gt},mt);var vt=function(){try{return"1"===1..toPrecision(void 0)}catch(e){return!0}}(),bt=p.toPrecision;R(p,{toPrecision:function(e){return"undefined"==typeof e?bt.call(this):bt.call(this,e)}},vt),2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||".".split(/()()/).length>1?!function(){var e="undefined"==typeof/()??/.exec("")[1],n=Math.pow(2,32)-1;u.split=function(r,i){var a=String(this);if("undefined"==typeof r&&0===i)return[];if(!t(r))return Q(this,r,i);var o,s,l,u,c=[],p=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(r.sticky?"y":""),h=0,f=new RegExp(r.source,p+"g");e||(o=new RegExp("^"+f.source+"$(?!\\s)",p));var m="undefined"==typeof i?n:B.ToUint32(i);for(s=f.exec(a);s&&(l=s.index+s[0].length,!(l>h&&(W(c,Y(a,h,s.index)),!e&&s.length>1&&s[0].replace(o,function(){for(var e=1;e<arguments.length-2;e++)"undefined"==typeof arguments[e]&&(s[e]=void 0)}),s.length>1&&s.index<a.length&&d.apply(c,H(s,1)),u=s[0].length,h=l,c.length>=m)));)f.lastIndex===s.index&&f.lastIndex++,s=f.exec(a);return h===a.length?!u&&f.test("")||W(c,""):W(c,Y(a,h)),c.length>m?H(c,0,m):c}}():"0".split(void 0,0).length&&(u.split=function(e,t){return"undefined"==typeof e&&0===t?[]:Q(this,e,t)});var wt=u.replace,xt=function(){var e=[];return"x".replace(/x(.)?/g,function(t,n){W(e,n)}),1===e.length&&"undefined"==typeof e[0]}();xt||(u.replace=function(n,r){var i=e(r),a=t(n)&&/\)[*?]/.test(n.source);if(i&&a){var o=function(e){var t=arguments.length,i=n.lastIndex;n.lastIndex=0;var a=n.exec(e)||[];return n.lastIndex=i,W(a,arguments[t-2],arguments[t-1]),r.apply(this,a)};return wt.call(this,n,o)}return wt.call(this,n,r)});var jt=u.substr,Ot="".substr&&"b"!=="0b".substr(-1);R(u,{substr:function(e,t){var n=e;return 0>e&&(n=w(this.length+e,0)),jt.call(this,n,t)}},Ot);var At="	\n\x0B\f\r   ᠎             　\u2028\u2029\ufeff",St="​",_t="["+At+"]",kt=new RegExp("^"+_t+_t+"*"),Et=new RegExp(_t+_t+"*$"),Ct=u.trim&&(At.trim()||!St.trim());R(u,{trim:function(){if("undefined"==typeof this||null===this)throw new TypeError("can't convert "+this+" to object");return l(this).replace(kt,"").replace(Et,"")}},Ct);var It=v.bind(String.prototype.trim),Tt=u.lastIndexOf&&-1!=="abcあい".lastIndexOf("あい",2);R(u,{lastIndexOf:function(e){if("undefined"==typeof this||null===this)throw new TypeError("can't convert "+this+" to object");for(var t=l(this),n=l(e),r=arguments.length>1?c(arguments[1]):NaN,i=F(r)?1/0:B.ToInteger(r),a=x(w(i,0),t.length),o=n.length,s=a+o;s>0;){s=w(0,s-o);var u=K(Y(t,s,a+o),n);if(-1!==u)return s+u}return-1}},Tt);var Mt=u.lastIndexOf;if(R(u,{lastIndexOf:function(e){return Mt.apply(this,arguments)}},1!==u.lastIndexOf.length),8===parseInt(At+"08")&&22===parseInt(At+"0x16")||(parseInt=function(e){var t=/^[\-+]?0[xX]/;return function(n,r){var i=It(String(n)),a=c(r)||(t.test(i)?16:10);return e(i,a)}}(parseInt)),1/parseFloat("-0")!==-(1/0)&&(parseFloat=function(e){return function(t){var n=It(String(t)),r=e(n);return 0===r&&"-"===Y(n,0,1)?-0:r}}(parseFloat)),"RangeError: test"!==String(new RangeError("test"))){var $t=function(){if("undefined"==typeof this||null===this)throw new TypeError("can't convert "+this+" to object");
var e=this.name;"undefined"==typeof e?e="Error":"string"!=typeof e&&(e=l(e));var t=this.message;return"undefined"==typeof t?t="":"string"!=typeof t&&(t=l(t)),e?t?e+": "+t:e:t};Error.prototype.toString=$t}if(L){var Ut=function(e,t){if(X(e,t)){var n=Object.getOwnPropertyDescriptor(e,t);n.configurable&&(n.enumerable=!1,Object.defineProperty(e,t,n))}};Ut(Error.prototype,"message"),""!==Error.prototype.message&&(Error.prototype.message=""),Ut(Error.prototype,"name")}if("/a/gim"!==String(/a/gim)){var Dt=function(){var e="/"+this.source+"/";return this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),e};RegExp.prototype.toString=Dt}}),Handlebars.registerHelper("sanitize",function(e){return e=e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,""),new Handlebars.SafeString(e)}),Handlebars.registerHelper("renderTextParam",function(e){var t,n="text",r="",i=e.type||e.schema.type||"",a="array"===i.toLowerCase()||e.allowMultiple,o=a&&Array.isArray(e["default"])?e["default"].join("\n"):e["default"],s=Object.keys(e).filter(function(e){return null!==e.match(/^X-data-/i)}).reduce(function(t,n){return t+=" "+n.substring(2,n.length)+"='"+e[n]+"'"},"");if("undefined"==typeof o&&(o=""),e.format&&"password"===e.format&&(n="password"),e.valueId&&(r=" id='"+e.valueId+"'"),("string"==typeof o||o instanceof String)&&(o=o.replace(/'/g,"&apos;")),a)t="<textarea class='body-textarea"+(e.required?" required":"")+"' name='"+e.name+"'"+r+s,t+=" placeholder='Provide multiple values in new lines"+(e.required?" (at least one required).":".")+"'>",t+=o+"</textarea>";else{var l="parameter";e.required&&(l+=" required"),t="<input class='"+l+"' minlength='"+(e.required?1:0)+"'",t+=" name='"+e.name+"' placeholder='"+(e.required?"(required)":"")+"'"+r+s,t+=" type='"+n+"' value='"+o+"'/>"}return new Handlebars.SafeString(t)}),Handlebars.registerHelper("ifCond",function(e,t,n,r){switch(t){case"==":return e==n?r.fn(this):r.inverse(this);case"===":return e===n?r.fn(this):r.inverse(this);case"<":return n>e?r.fn(this):r.inverse(this);case"<=":return n>=e?r.fn(this):r.inverse(this);case">":return e>n?r.fn(this):r.inverse(this);case">=":return e>=n?r.fn(this):r.inverse(this);case"&&":return e&&n?r.fn(this):r.inverse(this);case"||":return e||n?r.fn(this):r.inverse(this);default:return r.inverse(this)}}),this.Handlebars.templates.main=Handlebars.template({1:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression,s='  <div class="info_title">'+o(a(null!=(i=null!=e?e.info:e)?i.title:i,e))+'</div>\n  <div class="info_description markdown">';return i=a(null!=(i=null!=e?e.info:e)?i.description:i,e),null!=i&&(s+=i),s+="</div>\n",i=t["if"].call(e,null!=e?e.externalDocs:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+="  ",i=t["if"].call(e,null!=(i=null!=e?e.info:e)?i.termsOfServiceUrl:i,{name:"if",hash:{},fn:this.program(4,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+="\n  ",i=t["if"].call(e,null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.name:i,{name:"if",hash:{},fn:this.program(6,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+="\n  ",i=t["if"].call(e,null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.url:i,{name:"if",hash:{},fn:this.program(8,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+="\n  ",i=t["if"].call(e,null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.email:i,{name:"if",hash:{},fn:this.program(10,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+="\n  ",i=t["if"].call(e,null!=(i=null!=e?e.info:e)?i.license:i,{name:"if",hash:{},fn:this.program(12,r),inverse:this.noop,data:r}),null!=i&&(s+=i),s+"\n"},2:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return"  <p>"+o(a(null!=(i=null!=e?e.externalDocs:e)?i.description:i,e))+'</p>\n  <a href="'+o(a(null!=(i=null!=e?e.externalDocs:e)?i.url:i,e))+'" target="_blank">'+o(a(null!=(i=null!=e?e.externalDocs:e)?i.url:i,e))+"</a>\n"},4:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return'<div class="info_tos"><a target="_blank" href="'+o(a(null!=(i=null!=e?e.info:e)?i.termsOfServiceUrl:i,e))+'" data-sw-translate>Terms of service</a></div>'},6:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return"<div><div class='info_name' style=\"display: inline\" data-sw-translate>Created by </div> "+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.name:i,e))+"</div>"},8:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return"<div class='info_url' data-sw-translate>See more at <a href=\""+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.url:i,e))+'">'+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.url:i,e))+"</a></div>"},10:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return'<div class=\'info_email\'><a target="_parent" href="mailto:'+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.contact:i)?i.email:i,e))+"?subject="+o(a(null!=(i=null!=e?e.info:e)?i.title:i,e))+'" data-sw-translate>Contact the developer</a></div>'},12:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return"<div class='info_license'><a target=\"_blank\" href='"+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.license:i)?i.url:i,e))+"'>"+o(a(null!=(i=null!=(i=null!=e?e.info:e)?i.license:i)?i.name:i,e))+"</a></div>"},14:function(e,t,n,r){var i,a=this.lambda,o=this.escapeExpression;return'  , <span style="font-variant: small-caps" data-sw-translate>api version</span>: '+o(a(null!=(i=null!=e?e.info:e)?i.version:i,e))+"\n    "},16:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'    <span style="float:right"><a target="_blank" href="'+s((i=null!=(i=t.validatorUrl||(null!=e?e.validatorUrl:e))?i:o,typeof i===a?i.call(e,{name:"validatorUrl",hash:{},data:r}):i))+"/debug?url="+s((i=null!=(i=t.url||(null!=e?e.url:e))?i:o,typeof i===a?i.call(e,{name:"url",hash:{},data:r}):i))+'"><img id="validator" src="'+s((i=null!=(i=t.validatorUrl||(null!=e?e.validatorUrl:e))?i:o,typeof i===a?i.call(e,{name:"validatorUrl",hash:{},data:r}):i))+"?url="+s((i=null!=(i=t.url||(null!=e?e.url:e))?i:o,typeof i===a?i.call(e,{name:"url",hash:{},data:r}):i))+'"></a>\n    </span>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<div class='info' id='api_info'>\n";return i=t["if"].call(e,null!=e?e.info:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="</div>\n<div class='container' id='resources_container'>\n  <div class='authorize-wrapper'></div>\n\n  <ul id='resources'></ul>\n\n  <div class=\"footer\">\n    <h4 style=\"color: #999\">[ <span style=\"font-variant: small-caps\">base url</span>: "+l((a=null!=(a=t.basePath||(null!=e?e.basePath:e))?a:s,typeof a===o?a.call(e,{name:"basePath",hash:{},data:r}):a))+"\n",i=t["if"].call(e,null!=(i=null!=e?e.info:e)?i.version:i,{name:"if",hash:{},fn:this.program(14,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="]\n",i=t["if"].call(e,null!=e?e.validatorUrl:e,{name:"if",hash:{},fn:this.program(16,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"    </h4>\n    </div>\n</div>\n"},useData:!0}),this.Handlebars.templates.oauth2=Handlebars.template({1:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='            <li>\n                <input class="oauth-scope" type="checkbox" data-scope="'+l((a=null!=(a=t.scope||(null!=e?e.scope:e))?a:s,typeof a===o?a.call(e,{name:"scope",hash:{},data:r}):a))+'" oauthtype="'+l((a=null!=(a=t.OAuthSchemeKey||(null!=e?e.OAuthSchemeKey:e))?a:s,typeof a===o?a.call(e,{name:"OAuthSchemeKey",hash:{},data:r}):a))+'"/>\n                <label>'+l((a=null!=(a=t.scope||(null!=e?e.scope:e))?a:s,typeof a===o?a.call(e,{name:"scope",hash:{},data:r}):a))+'</label><br/>\n                <span class="api-scope-desc">'+l((a=null!=(a=t.description||(null!=e?e.description:e))?a:s,typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a))+"\n";return i=t["if"].call(e,null!=e?e.OAuthSchemeKey:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"                </span>\n            </li>\n"},2:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"                        ("+s((i=null!=(i=t.OAuthSchemeKey||(null!=e?e.OAuthSchemeKey:e))?i:o,typeof i===a?i.call(e,{name:"OAuthSchemeKey",hash:{},data:r}):i))+")\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='<div>\n    <h3 class="auth__title">Select OAuth2.0 Scopes</h3>\n    <p>'+l((a=null!=(a=t.description||(null!=e?e.description:e))?a:s,typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a))+'</p>\n    <p>Scopes are used to grant an application different levels of access to data on behalf of the end user. Each API may declare one or more scopes.\n        <a href="#">Learn how to use</a>\n    </p>\n    <p><strong> '+l((a=null!=(a=t.appName||(null!=e?e.appName:e))?a:s,typeof a===o?a.call(e,{name:"appName",hash:{},data:r}):a))+" </strong> API requires the following scopes. Select which ones you want to grant to Swagger UI.</p>\n    <p>Authorization URL: "+l((a=null!=(a=t.authorizationUrl||(null!=e?e.authorizationUrl:e))?a:s,typeof a===o?a.call(e,{name:"authorizationUrl",hash:{},data:r}):a))+"</p>\n    <p>flow: "+l((a=null!=(a=t.flow||(null!=e?e.flow:e))?a:s,typeof a===o?a.call(e,{name:"flow",hash:{},data:r}):a))+'</p>\n    <ul class="api-popup-scopes">\n';return i=t.each.call(e,null!=e?e.scopes:e,{name:"each",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"    </ul>\n</div>"},useData:!0}),this.Handlebars.templates.operation=Handlebars.template({1:function(e,t,n,r){return"deprecated"},3:function(e,t,n,r){return"            <h4><span data-sw-translate>Warning: Deprecated</span></h4>\n"},5:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l='        <h4><span data-sw-translate>Implementation Notes</span></h4>\n        <div class="markdown">';return a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(l+=i),l+"</div>\n"},7:function(e,t,n,r){return"            <div class='authorize-wrapper authorize-wrapper_operation'></div>\n"},9:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='          <div class="response-class">\n            <h4><span data-sw-translate>Response Class</span> (<span data-sw-translate>Status</span> '+l((a=null!=(a=t.successCode||(null!=e?e.successCode:e))?a:s,typeof a===o?a.call(e,{name:"successCode",hash:{},data:r}):a))+")</h4>\n              ";return i=t["if"].call(e,null!=e?e.successDescription:e,{name:"if",hash:{},fn:this.program(10,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+'\n            <p><span class="model-signature" /></p>\n            <br/>\n            <div class="response-content-type" />\n            </div>\n'},10:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l='<div class="markdown">';return a=null!=(a=t.successDescription||(null!=e?e.successDescription:e))?a:s,i=typeof a===o?a.call(e,{name:"successDescription",hash:{},data:r}):a,null!=i&&(l+=i),l+"</div>"},12:function(e,t,n,r){var i,a='          <h4 data-sw-translate>Headers</h4>\n          <table class="headers">\n            <thead>\n              <tr>\n                <th style="width: 100px; max-width: 100px" data-sw-translate>Header</th>\n                <th style="width: 310px; max-width: 310px" data-sw-translate>Description</th>\n                <th style="width: 200px; max-width: 200px" data-sw-translate>Type</th>\n                <th style="width: 320px; max-width: 320px" data-sw-translate>Other</th>\n              </tr>\n            </thead>\n            <tbody>\n';return i=t.each.call(e,null!=e?e.headers:e,{name:"each",hash:{},fn:this.program(13,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a+"            </tbody>\n          </table>\n"},13:function(e,t,n,r){var i=this.lambda,a=this.escapeExpression;return"              <tr>\n                <td>"+a(i(r&&r.key,e))+"</td>\n                <td>"+a(i(null!=e?e.description:e,e))+"</td>\n                <td>"+a(i(null!=e?e.type:e,e))+"</td>\n                <td>"+a(i(null!=e?e.other:e,e))+"</td>\n              </tr>\n"},15:function(e,t,n,r){return'          <h4 data-sw-translate>Parameters</h4>\n          <table class=\'fullwidth parameters\'>\n          <thead>\n            <tr>\n            <th style="width: 100px; max-width: 100px" data-sw-translate>Parameter</th>\n            <th style="width: 310px; max-width: 310px" data-sw-translate>Value</th>\n            <th style="width: 200px; max-width: 200px" data-sw-translate>Description</th>\n            <th style="width: 100px; max-width: 100px" data-sw-translate>Parameter Type</th>\n            <th style="width: 220px; max-width: 230px" data-sw-translate>Data Type</th>\n            </tr>\n          </thead>\n          <tbody class="operation-params">\n\n          </tbody>\n          </table>\n'},17:function(e,t,n,r){return"          <div style='margin:0;padding:0;display:inline'></div>\n          <h4 data-sw-translate>Response Messages</h4>\n          <table class='fullwidth response-messages'>\n            <thead>\n            <tr>\n              <th data-sw-translate>HTTP Status Code</th>\n              <th data-sw-translate>Reason</th>\n              <th data-sw-translate>Response Model</th>\n              <th data-sw-translate>Headers</th>\n            </tr>\n            </thead>\n            <tbody class=\"operation-status\">\n            </tbody>\n          </table>\n"},19:function(e,t,n,r){return""},21:function(e,t,n,r){return"          <div class='sandbox_header'>\n            <input class='submit' type='submit' value='Try it out!' data-sw-translate/>\n            <a href='#' class='response_hider' style='display:none' data-sw-translate>Hide Response</a>\n            <span class='response_throbber' style='display:none'></span>\n          </div>\n"},23:function(e,t,n,r){return"          <h4 data-sw-translate>Request Headers</h4>\n          <div class='block request_headers'></div>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="  <ul class='operations' >\n    <li class='"+l((a=null!=(a=t.method||(null!=e?e.method:e))?a:s,typeof a===o?a.call(e,{name:"method",hash:{},data:r}):a))+" operation' id='"+l((a=null!=(a=t.parentId||(null!=e?e.parentId:e))?a:s,typeof a===o?a.call(e,{name:"parentId",hash:{},data:r}):a))+"_"+l((a=null!=(a=t.nickname||(null!=e?e.nickname:e))?a:s,typeof a===o?a.call(e,{name:"nickname",hash:{},data:r}):a))+"'>\n      <div class='heading'>\n        <h3>\n          <span class='http_method'>\n          <a href='#!/"+l((a=null!=(a=t.encodedParentId||(null!=e?e.encodedParentId:e))?a:s,typeof a===o?a.call(e,{name:"encodedParentId",hash:{},data:r}):a))+"/"+l((a=null!=(a=t.nickname||(null!=e?e.nickname:e))?a:s,typeof a===o?a.call(e,{name:"nickname",hash:{},data:r}):a))+'\' class="toggleOperation">'+l((a=null!=(a=t.method||(null!=e?e.method:e))?a:s,typeof a===o?a.call(e,{name:"method",hash:{},data:r}):a))+"</a>\n          </span>\n          <span class='path'>\n          <a href='#!/"+l((a=null!=(a=t.encodedParentId||(null!=e?e.encodedParentId:e))?a:s,typeof a===o?a.call(e,{name:"encodedParentId",hash:{},data:r}):a))+"/"+l((a=null!=(a=t.nickname||(null!=e?e.nickname:e))?a:s,typeof a===o?a.call(e,{name:"nickname",hash:{},data:r}):a))+"' class=\"toggleOperation ";return i=t["if"].call(e,null!=e?e.deprecated:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+='">'+l((a=null!=(a=t.path||(null!=e?e.path:e))?a:s,typeof a===o?a.call(e,{name:"path",hash:{},data:r}):a))+"</a>\n          </span>\n        </h3>\n        <ul class='options'>\n          <li>\n          <a href='#!/"+l((a=null!=(a=t.encodedParentId||(null!=e?e.encodedParentId:e))?a:s,typeof a===o?a.call(e,{name:"encodedParentId",hash:{},data:r}):a))+"/"+l((a=null!=(a=t.nickname||(null!=e?e.nickname:e))?a:s,typeof a===o?a.call(e,{name:"nickname",hash:{},data:r}):a))+'\' class="toggleOperation">',a=null!=(a=t.summary||(null!=e?e.summary:e))?a:s,i=typeof a===o?a.call(e,{name:"summary",hash:{},data:r}):a,null!=i&&(u+=i),u+="</a>\n          </li>\n        </ul>\n      </div>\n      <div class='content' id='"+l((a=null!=(a=t.parentId||(null!=e?e.parentId:e))?a:s,typeof a===o?a.call(e,{name:"parentId",hash:{},data:r}):a))+"_"+l((a=null!=(a=t.nickname||(null!=e?e.nickname:e))?a:s,typeof a===o?a.call(e,{name:"nickname",hash:{},data:r}):a))+"_content' style='display:none'>\n",i=t["if"].call(e,null!=e?e.deprecated:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.description:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.noop,data:r}),null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.security:e,{name:"if",hash:{},fn:this.program(7,r),inverse:this.noop,data:r}),null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.type:e,{name:"if",hash:{},fn:this.program(9,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="\n",i=t["if"].call(e,null!=e?e.headers:e,{name:"if",hash:{},fn:this.program(12,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="\n        <form accept-charset='UTF-8' class='sandbox'>\n          <div style='margin:0;padding:0;display:inline'></div>\n",i=t["if"].call(e,null!=e?e.parameters:e,{name:"if",hash:{},fn:this.program(15,r),inverse:this.noop,data:r}),null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.responseMessages:e,{name:"if",hash:{},fn:this.program(17,r),inverse:this.noop,data:r}),null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.isReadOnly:e,{name:"if",hash:{},fn:this.program(19,r),inverse:this.program(21,r),data:r}),null!=i&&(u+=i),u+="        </form>\n        <div class='response' style='display:none'>\n          <h4 class='curl'>Curl</h4>\n          <div class='block curl'></div>\n          <h4 data-sw-translate>Request URL</h4>\n          <div class='block request_url'></div>\n",i=t["if"].call(e,null!=e?e.showRequestHeaders:e,{name:"if",hash:{},fn:this.program(23,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"          <h4 data-sw-translate>Response Body</h4>\n          <div class='block response_body'></div>\n          <h4 data-sw-translate>Response Code</h4>\n          <div class='block response_code'></div>\n          <h4 data-sw-translate>Response Headers</h4>\n          <div class='block response_headers'></div>\n        </div>\n      </div>\n    </li>\n  </ul>\n"},useData:!0}),this.Handlebars.templates.param_list=Handlebars.template({1:function(e,t,n,r){return" required"},3:function(e,t,n,r){return' multiple="multiple"'},5:function(e,t,n,r){return" required "},7:function(e,t,n,r){var i,a="      <option ";return i=t.unless.call(e,null!=e?e.hasDefault:e,{name:"unless",hash:{},fn:this.program(8,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a+" value=''></option>\n"},8:function(e,t,n,r){return'  selected="" '},10:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="\n      <option ";return i=t["if"].call(e,null!=e?e.isDefault:e,{name:"if",hash:{},fn:this.program(11,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="  value='"+l((a=null!=(a=t.value||(null!=e?e.value:e))?a:s,typeof a===o?a.call(e,{name:"value",hash:{},data:r}):a))+"'> "+l((a=null!=(a=t.value||(null!=e?e.value:e))?a:s,typeof a===o?a.call(e,{name:"value",hash:{},data:r}):a))+" ",i=t["if"].call(e,null!=e?e.isDefault:e,{name:"if",hash:{},fn:this.program(13,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+" </option>\n\n"},11:function(e,t,n,r){return' selected=""  '},13:function(e,t,n,r){return" (default) "},15:function(e,t,n,r){return"<strong>"},17:function(e,t,n,r){return"</strong>"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td class='code";return i=t["if"].call(e,null!=e?e.required:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="'><label for='"+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+"'>"+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+"</label></td>\n<td>\n  <select ",i=(t.isArray||e&&e.isArray||s).call(e,e,{name:"isArray",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+=' class="parameter ',i=t["if"].call(e,null!=e?e.required:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+='" name="'+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+'" id="'+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+'">\n\n',i=t.unless.call(e,null!=e?e.required:e,{name:"unless",hash:{},fn:this.program(7,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="\n",i=t.each.call(e,null!=(i=null!=e?e.allowableValues:e)?i.descriptiveValues:i,{name:"each",hash:{},fn:this.program(10,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+='\n  </select>\n</td>\n<td class="markdown">',i=t["if"].call(e,null!=e?e.required:e,{name:"if",hash:{},fn:this.program(15,r),inverse:this.noop,data:r}),null!=i&&(u+=i),a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(u+=i),i=t["if"].call(e,null!=e?e.required:e,{name:"if",hash:{},fn:this.program(17,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+="</td>\n<td>",a=null!=(a=t.paramType||(null!=e?e.paramType:e))?a:s,i=typeof a===o?a.call(e,{name:"paramType",hash:{},data:r}):a,null!=i&&(u+=i),u+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.param_readonly_required=Handlebars.template({1:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"        <textarea class='body-textarea' readonly='readonly' placeholder='(required)' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+"'>"+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+"</textarea>\n"},3:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(4,r),inverse:this.program(6,r),data:r}),null!=i&&(a+=i),a},4:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"            "+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+"\n"},6:function(e,t,n,r){return"            (empty)\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td class='code required'><label for='"+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+"'>"+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+"</label></td>\n<td>\n";return i=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(3,r),data:r}),null!=i&&(u+=i),u+='</td>\n<td class="markdown">',a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(u+=i),u+="</td>\n<td>",a=null!=(a=t.paramType||(null!=e?e.paramType:e))?a:s,i=typeof a===o?a.call(e,{name:"paramType",hash:{},data:r}):a,null!=i&&(u+=i),u+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.param_readonly=Handlebars.template({1:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"        <textarea class='body-textarea' readonly='readonly' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+"'>"+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+'</textarea>\n        <div class="parameter-content-type" />\n'},3:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(4,r),inverse:this.program(6,r),data:r}),null!=i&&(a+=i),a},4:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"            "+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+"\n"},6:function(e,t,n,r){return"            (empty)\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td class='code'><label for='"+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+"'>"+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+"</label></td>\n<td>\n";return i=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(3,r),data:r}),null!=i&&(u+=i),u+='</td>\n<td class="markdown">',a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(u+=i),u+="</td>\n<td>",a=null!=(a=t.paramType||(null!=e?e.paramType:e))?a:s,i=typeof a===o?a.call(e,{name:"paramType",hash:{},data:r}):a,null!=i&&(u+=i),u+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.param_required=Handlebars.template({1:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.program(4,r),data:r}),null!=i&&(a+=i),a},2:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'			<input type="file" name=\''+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+"'/>\n"},4:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.program(7,r),data:r}),null!=i&&(a+=i),a},5:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"				<div class=\"editor_holder\"></div>\n				<textarea class='body-textarea required' placeholder='(required)' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id=\""+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+'">'+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+'</textarea>\n        <br />\n        <div class="parameter-content-type" />\n'},7:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"				<textarea class='body-textarea required' placeholder='(required)' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+'\'></textarea>\n				<div class="editor_holder"></div>\n				<br />\n				<div class="parameter-content-type" />\n'},9:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(10,r),inverse:this.program(12,r),data:r}),null!=i&&(a+=i),a},10:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"			<input class='parameter' class='required' type='file' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+"'/>\n"},12:function(e,t,n,r){var i,a=t.helperMissing,o="";return i=(t.renderTextParam||e&&e.renderTextParam||a).call(e,e,{name:"renderTextParam",hash:{},fn:this.program(13,r),inverse:this.noop,data:r}),null!=i&&(o+=i),o},13:function(e,t,n,r){return""},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td class='code required'><label for='"+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+"'>"+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+"</label></td>\n<td>\n";return i=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(9,r),data:r}),null!=i&&(u+=i),u+='</td>\n<td>\n	<strong><span class="markdown">',a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(u+=i),u+="</span></strong>\n</td>\n<td>",a=null!=(a=t.paramType||(null!=e?e.paramType:e))?a:s,i=typeof a===o?a.call(e,{name:"paramType",hash:{},data:r}):a,null!=i&&(u+=i),u+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.param=Handlebars.template({1:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.program(4,r),data:r}),null!=i&&(a+=i),a},2:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'			<input type="file" name=\''+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+'\'/>\n			<div class="parameter-content-type" />\n'},4:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.program(7,r),data:r}),null!=i&&(a+=i),a},5:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"				<div class=\"editor_holder\"></div>\n				<textarea class='body-textarea' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+"'>"+s((i=null!=(i=t["default"]||(null!=e?e["default"]:e))?i:o,typeof i===a?i.call(e,{name:"default",hash:{},data:r}):i))+'</textarea>\n        <br />\n        <div class="parameter-content-type" />\n'},7:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"				<textarea class='body-textarea' name='"+s((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===a?i.call(e,{name:"name",hash:{},data:r}):i))+"' id='"+s((i=null!=(i=t.valueId||(null!=e?e.valueId:e))?i:o,typeof i===a?i.call(e,{name:"valueId",hash:{},data:r}):i))+'\'></textarea>\n				<div class="editor_holder"></div>\n				<br />\n				<div class="parameter-content-type" />\n'},9:function(e,t,n,r){var i,a="";return i=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.program(10,r),data:r}),null!=i&&(a+=i),a},10:function(e,t,n,r){var i,a=t.helperMissing,o="";return i=(t.renderTextParam||e&&e.renderTextParam||a).call(e,e,{name:"renderTextParam",hash:{},fn:this.program(11,r),inverse:this.noop,data:r}),null!=i&&(o+=i),o},11:function(e,t,n,r){return""},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td class='code'><label for='"+l((a=null!=(a=t.valueId||(null!=e?e.valueId:e))?a:s,typeof a===o?a.call(e,{name:"valueId",hash:{},data:r}):a))+"'>"+l((a=null!=(a=t.name||(null!=e?e.name:e))?a:s,typeof a===o?a.call(e,{name:"name",hash:{},data:r}):a))+"</label></td>\n<td>\n\n";
return i=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(9,r),data:r}),null!=i&&(u+=i),u+='\n</td>\n<td class="markdown">',a=null!=(a=t.description||(null!=e?e.description:e))?a:s,i=typeof a===o?a.call(e,{name:"description",hash:{},data:r}):a,null!=i&&(u+=i),u+="</td>\n<td>",a=null!=(a=t.paramType||(null!=e?e.paramType:e))?a:s,i=typeof a===o?a.call(e,{name:"paramType",hash:{},data:r}):a,null!=i&&(u+=i),u+'</td>\n<td>\n	<span class="model-signature"></span>\n</td>\n'},useData:!0}),this.Handlebars.templates.parameter_content_type=Handlebars.template({1:function(e,t,n,r){var i,a="";return i=t.each.call(e,null!=e?e.consumes:e,{name:"each",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a},2:function(e,t,n,r){var i=this.lambda,a=this.escapeExpression;return'  <option value="'+a(i(e,e))+'">'+a(i(e,e))+"</option>\n"},4:function(e,t,n,r){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='<label for="'+l((a=null!=(a=t.parameterContentTypeId||(null!=e?e.parameterContentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"parameterContentTypeId",hash:{},data:r}):a))+'" data-sw-translate>Parameter content type:</label>\n<select name="parameterContentType" id="'+l((a=null!=(a=t.parameterContentTypeId||(null!=e?e.parameterContentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"parameterContentTypeId",hash:{},data:r}):a))+'">\n';return i=t["if"].call(e,null!=e?e.consumes:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(4,r),data:r}),null!=i&&(u+=i),u+"</select>\n"},useData:!0}),this.Handlebars.templates.popup=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return'<div class="api-popup-dialog-wrapper">\n    <div class="api-popup-title">'+s((i=null!=(i=t.title||(null!=e?e.title:e))?i:o,typeof i===a?i.call(e,{name:"title",hash:{},data:r}):i))+'</div>\n    <div class="api-popup-content"></div>\n    <p class="error-msg"></p>\n    <div class="api-popup-actions">\n        <button class="api-popup-cancel api-button gray" type="button">Cancel</button>\n    </div>\n</div>\n<div class="api-popup-dialog-shadow"></div>'},useData:!0}),this.Handlebars.templates.resource=Handlebars.template({1:function(e,t,n,r){return" : "},3:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"    <li>\n      <a href='"+s((i=null!=(i=t.url||(null!=e?e.url:e))?i:o,typeof i===a?i.call(e,{name:"url",hash:{},data:r}):i))+"' data-sw-translate>Raw</a>\n    </li>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o,s="function",l=t.helperMissing,u=this.escapeExpression,c=t.blockHelperMissing,p="<div class='heading'>\n  <h2>\n    <a href='#!/"+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'\' class="toggleEndpointList" data-id="'+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'">'+u((a=null!=(a=t.name||(null!=e?e.name:e))?a:l,typeof a===s?a.call(e,{name:"name",hash:{},data:r}):a))+"</a> ";return a=null!=(a=t.summary||(null!=e?e.summary:e))?a:l,o={name:"summary",hash:{},fn:this.program(1,r),inverse:this.noop,data:r},i=typeof a===s?a.call(e,o):a,t.summary||(i=c.call(e,i,o)),null!=i&&(p+=i),a=null!=(a=t.summary||(null!=e?e.summary:e))?a:l,i=typeof a===s?a.call(e,{name:"summary",hash:{},data:r}):a,null!=i&&(p+=i),p+="\n  </h2>\n  <ul class='options'>\n    <li>\n      <a href='#!/"+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+"' id='endpointListTogger_"+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'\' class="toggleEndpointList" data-id="'+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'" data-sw-translate>Show/Hide</a>\n    </li>\n    <li>\n      <a href=\'#\' class="collapseResource" data-id="'+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'" data-sw-translate>\n        List Operations\n      </a>\n    </li>\n    <li>\n      <a href=\'#\' class="expandResource" data-id="'+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+'" data-sw-translate>\n        Expand Operations\n      </a>\n    </li>\n',i=t["if"].call(e,null!=e?e.url:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(p+=i),p+"  </ul>\n</div>\n<ul class='endpoints' id='"+u((a=null!=(a=t.id||(null!=e?e.id:e))?a:l,typeof a===s?a.call(e,{name:"id",hash:{},data:r}):a))+"_endpoint_list' style='display:none'>\n\n</ul>\n"},useData:!0}),this.Handlebars.templates.response_content_type=Handlebars.template({1:function(e,t,n,r){var i,a="";return i=t.each.call(e,null!=e?e.produces:e,{name:"each",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(a+=i),a},2:function(e,t,n,r){var i=this.lambda,a=this.escapeExpression;return'  <option value="'+a(i(e,e))+'">'+a(i(e,e))+"</option>\n"},4:function(e,t,n,r){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='<label data-sw-translate for="'+l((a=null!=(a=t.responseContentTypeId||(null!=e?e.responseContentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"responseContentTypeId",hash:{},data:r}):a))+'">Response Content Type</label>\n<select name="responseContentType" id="'+l((a=null!=(a=t.responseContentTypeId||(null!=e?e.responseContentTypeId:e))?a:s,typeof a===o?a.call(e,{name:"responseContentTypeId",hash:{},data:r}):a))+'">\n';return i=t["if"].call(e,null!=e?e.produces:e,{name:"if",hash:{},fn:this.program(1,r),inverse:this.program(4,r),data:r}),null!=i&&(u+=i),u+"</select>\n"},useData:!0}),this.Handlebars.templates.signature=Handlebars.template({1:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l='\n<div>\n<ul class="signature-nav">\n  <li><a class="description-link" href="#" data-sw-translate>Model</a></li>\n  <li><a class="snippet-link" href="#" data-sw-translate>Example Value</a></li>\n</ul>\n<div>\n\n<div class="signature-container">\n  <div class="description">\n    ';return a=null!=(a=t.signature||(null!=e?e.signature:e))?a:s,i=typeof a===o?a.call(e,{name:"signature",hash:{},data:r}):a,null!=i&&(l+=i),l+='\n  </div>\n\n  <div class="snippet">\n',i=t["if"].call(e,null!=e?e.sampleJSON:e,{name:"if",hash:{},fn:this.program(2,r),inverse:this.noop,data:r}),null!=i&&(l+=i),i=t["if"].call(e,null!=e?e.sampleXML:e,{name:"if",hash:{},fn:this.program(5,r),inverse:this.noop,data:r}),null!=i&&(l+=i),l+"  </div>\n</div>\n"},2:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='      <div class="snippet_json">\n        <pre><code>'+l((a=null!=(a=t.sampleJSON||(null!=e?e.sampleJSON:e))?a:s,typeof a===o?a.call(e,{name:"sampleJSON",hash:{},data:r}):a))+"</code></pre>\n        ";return i=t["if"].call(e,null!=e?e.isParam:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"\n      </div>\n"},3:function(e,t,n,r){return'<small class="notice" data-sw-translate></small>'},5:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u='    <div class="snippet_xml">\n      <pre><code>'+l((a=null!=(a=t.sampleXML||(null!=e?e.sampleXML:e))?a:s,typeof a===o?a.call(e,{name:"sampleXML",hash:{},data:r}):a))+"</code></pre>\n      ";return i=t["if"].call(e,null!=e?e.isParam:e,{name:"if",hash:{},fn:this.program(3,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"\n    </div>\n"},7:function(e,t,n,r){var i,a="function",o=t.helperMissing,s=this.escapeExpression;return"    "+s((i=null!=(i=t.signature||(null!=e?e.signature:e))?i:o,typeof i===a?i.call(e,{name:"signature",hash:{},data:r}):i))+"\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a=t.helperMissing;return i=(t.ifCond||e&&e.ifCond||a).call(e,null!=e?e.sampleJSON:e,"||",null!=e?e.sampleXML:e,{name:"ifCond",hash:{},fn:this.program(1,r),inverse:this.program(7,r),data:r}),null!=i?i:""},useData:!0}),this.Handlebars.templates.status_code=Handlebars.template({1:function(e,t,n,r){var i=this.lambda,a=this.escapeExpression;return"      <tr>\n        <td>"+a(i(r&&r.key,e))+"</td>\n        <td>"+a(i(null!=e?e.description:e,e))+"</td>\n        <td>"+a(i(null!=e?e.type:e,e))+"</td>\n      </tr>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,r){var i,a,o="function",s=t.helperMissing,l=this.escapeExpression,u="<td width='15%' class='code'>"+l((a=null!=(a=t.code||(null!=e?e.code:e))?a:s,typeof a===o?a.call(e,{name:"code",hash:{},data:r}):a))+'</td>\n<td class="markdown">';return a=null!=(a=t.message||(null!=e?e.message:e))?a:s,i=typeof a===o?a.call(e,{name:"message",hash:{},data:r}):a,null!=i&&(u+=i),u+='</td>\n<td width=\'50%\'><span class="model-signature" /></td>\n<td class="headers">\n  <table>\n    <tbody>\n',i=t.each.call(e,null!=e?e.headers:e,{name:"each",hash:{},fn:this.program(1,r),inverse:this.noop,data:r}),null!=i&&(u+=i),u+"    </tbody>\n  </table>\n</td>"},useData:!0}),function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.SwaggerClient=e()}}(function(){var t;return function n(e,t,r){function i(o,s){if(!t[o]){if(!e[o]){var l="function"==typeof require&&require;if(!s&&l)return l(o,!0);if(a)return a(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var c=t[o]={exports:{}};e[o][0].call(c.exports,function(t){var n=e[o][1][t];return i(n?n:t)},c,c.exports,n,e,t,r)}return t[o].exports}for(var a="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){"use strict";var r=e("./lib/auth"),i=e("./lib/helpers"),a=e("./lib/client"),o=function(e,t){return i.log('This is deprecated, use "new SwaggerClient" instead.'),new a(e,t)};Array.prototype.indexOf||(Array.prototype.indexOf=function(e,t){for(var n=t||0,r=this.length;r>n;n++)if(this[n]===e)return n;return-1}),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),String.prototype.endsWith||(String.prototype.endsWith=function(e){return-1!==this.indexOf(e,this.length-e.length)}),t.exports=a,a.ApiKeyAuthorization=r.ApiKeyAuthorization,a.PasswordAuthorization=r.PasswordAuthorization,a.CookieAuthorization=r.CookieAuthorization,a.SwaggerApi=o,a.SwaggerClient=o,a.SchemaMarkup=e("./lib/schema-markup")},{"./lib/auth":2,"./lib/client":3,"./lib/helpers":4,"./lib/schema-markup":7}],2:[function(e,t,n){"use strict";var r=e("./helpers"),i=e("btoa"),a=e("cookiejar").CookieJar,o={each:e("lodash-compat/collection/each"),includes:e("lodash-compat/collection/includes"),isObject:e("lodash-compat/lang/isObject"),isArray:e("lodash-compat/lang/isArray")},s=t.exports.SwaggerAuthorizations=function(e){this.authz=e||{}};s.prototype.add=function(e,t){if(o.isObject(e))for(var n in e)this.authz[n]=e[n];else"string"==typeof e&&(this.authz[e]=t);return t},s.prototype.remove=function(e){return delete this.authz[e]},s.prototype.apply=function(e,t){var n=!0,r=!t,i=[],a=e.clientAuthorizations||this.authz;return o.each(t,function(e,t){"string"==typeof t&&i.push(t),o.each(e,function(e,t){i.push(t)})}),o.each(a,function(t,a){if(r||o.includes(i,a)){var s=t.apply(e);n=n&&!!s}}),n};var l=t.exports.ApiKeyAuthorization=function(e,t,n){this.name=e,this.value=t,this.type=n};l.prototype.apply=function(e){if("query"===this.type){var t;if(e.url.indexOf("?")>0){t=e.url.substring(e.url.indexOf("?")+1);var n=t.split("&");if(n&&n.length>0)for(var r=0;r<n.length;r++){var i=n[r].split("=");if(i&&i.length>0&&i[0]===this.name)return!1}}return e.url.indexOf("?")>0?e.url=e.url+"&"+this.name+"="+this.value:e.url=e.url+"?"+this.name+"="+this.value,!0}return"header"===this.type?("undefined"==typeof e.headers[this.name]&&(e.headers[this.name]=this.value),!0):void 0};var u=t.exports.CookieAuthorization=function(e){this.cookie=e};u.prototype.apply=function(e){return e.cookieJar=e.cookieJar||new a,e.cookieJar.setCookie(this.cookie),!0};var c=t.exports.PasswordAuthorization=function(e,t){3===arguments.length&&(r.log("PasswordAuthorization: the 'name' argument has been removed, pass only username and password"),e=arguments[1],t=arguments[2]),this.username=e,this.password=t};c.prototype.apply=function(e){return"undefined"==typeof e.headers.Authorization&&(e.headers.Authorization="Basic "+i(this.username+":"+this.password)),!0}},{"./helpers":4,btoa:13,cookiejar:18,"lodash-compat/collection/each":52,"lodash-compat/collection/includes":55,"lodash-compat/lang/isArray":140,"lodash-compat/lang/isObject":144}],3:[function(e,t,n){"use strict";var r={bind:e("lodash-compat/function/bind"),cloneDeep:e("lodash-compat/lang/cloneDeep"),find:e("lodash-compat/collection/find"),forEach:e("lodash-compat/collection/forEach"),indexOf:e("lodash-compat/array/indexOf"),isArray:e("lodash-compat/lang/isArray"),isObject:e("lodash-compat/lang/isObject"),isFunction:e("lodash-compat/lang/isFunction"),isPlainObject:e("lodash-compat/lang/isPlainObject"),isUndefined:e("lodash-compat/lang/isUndefined")},i=e("./auth"),a=e("./helpers"),o=e("./types/model"),s=e("./types/operation"),l=e("./types/operationGroup"),u=e("./resolver"),c=e("./http"),p=e("./spec-converter"),h=e("q"),f=["apis","authorizationScheme","authorizations","basePath","build","buildFrom1_1Spec","buildFrom1_2Spec","buildFromSpec","clientAuthorizations","convertInfo","debug","defaultErrorCallback","defaultSuccessCallback","enableCookies","fail","failure","finish","help","host","idFromOp","info","initialize","isBuilt","isValid","modelPropertyMacro","models","modelsArray","options","parameterMacro","parseUri","progress","resourceCount","sampleModels","selfReflect","setConsolidatedModels","spec","supportedSubmitMethods","swaggerRequestHeaders","tagFromLabel","title","url","useJQuery","jqueryAjaxCache"],d=["apis","asCurl","description","externalDocs","help","label","name","operation","operations","operationsArray","path","tag"],m=["delete","get","head","options","patch","post","put"],y=t.exports=function(e,t){return this.authorizations=null,this.authorizationScheme=null,this.basePath=null,this.debug=!1,this.enableCookies=!1,this.info=null,this.isBuilt=!1,this.isValid=!1,this.modelsArray=[],this.resourceCount=0,this.url=null,this.useJQuery=!1,this.jqueryAjaxCache=!1,this.swaggerObject={},this.deferredClient=void 0,this.clientAuthorizations=new i.SwaggerAuthorizations,"undefined"!=typeof e?this.initialize(e,t):this};y.prototype.initialize=function(e,t){return this.models={},this.sampleModels={},"string"==typeof e?this.url=e:r.isObject(e)&&(t=e,this.url=t.url),this.url&&-1===this.url.indexOf("http:")&&-1===this.url.indexOf("https:")&&"undefined"!=typeof window&&window&&window.location&&(this.url=window.location.origin+this.url),t=t||{},this.clientAuthorizations.add(t.authorizations),this.swaggerRequestHeaders=t.swaggerRequestHeaders||"application/json;charset=utf-8,*/*",this.defaultSuccessCallback=t.defaultSuccessCallback||null,this.defaultErrorCallback=t.defaultErrorCallback||null,this.modelPropertyMacro=t.modelPropertyMacro||null,this.parameterMacro=t.parameterMacro||null,this.usePromise=t.usePromise||null,this.usePromise&&(this.deferredClient=h.defer()),"function"==typeof t.success&&(this.success=t.success),t.useJQuery&&(this.useJQuery=t.useJQuery),t.jqueryAjaxCache&&(this.jqueryAjaxCache=t.jqueryAjaxCache),t.enableCookies&&(this.enableCookies=t.enableCookies),this.options=t||{},this.supportedSubmitMethods=t.supportedSubmitMethods||[],this.failure=t.failure||function(e){throw e},this.progress=t.progress||function(){},this.spec=r.cloneDeep(t.spec),t.scheme&&(this.scheme=t.scheme),this.usePromise||"function"==typeof t.success?(this.ready=!0,this.build()):void 0},y.prototype.build=function(e){if(this.isBuilt)return this;var t=this;this.spec?this.progress("fetching resource list; Please wait."):this.progress("fetching resource list: "+this.url+"; Please wait.");var n={useJQuery:this.useJQuery,jqueryAjaxCache:this.jqueryAjaxCache,url:this.url,method:"get",headers:{accept:this.swaggerRequestHeaders},on:{error:function(e){return"http"!==t.url.substring(0,4)?t.fail("Please specify the protocol for "+t.url):0===e.status?t.fail("Can't read from server.  It may not have the appropriate access-control-origin settings."):404===e.status?t.fail("Can't read swagger JSON from "+t.url):t.fail(e.status+" : "+e.statusText+" "+t.url)},response:function(e){var n=e.obj;if(!n)return t.fail("failed to parse JSON/YAML response");if(t.swaggerVersion=n.swaggerVersion,t.swaggerObject=n,n.swagger&&2===parseInt(n.swagger))t.swaggerVersion=n.swagger,(new u).resolve(n,t.url,t.buildFromSpec,t),t.isValid=!0;else{var r=new p;t.oldSwaggerObject=t.swaggerObject,r.setDocumentationLocation(t.url),r.convert(n,t.clientAuthorizations,t.options,function(e){t.swaggerObject=e,(new u).resolve(e,t.url,t.buildFromSpec,t),t.isValid=!0})}}}};if(this.spec)t.swaggerObject=this.spec,setTimeout(function(){(new u).resolve(t.spec,t.url,t.buildFromSpec,t)},10);else{if(this.clientAuthorizations.apply(n),e)return n;(new c).execute(n,this.options)}return this.usePromise?this.deferredClient.promise:this},y.prototype.buildFromSpec=function(e){if(this.isBuilt)return this;this.apis={},this.apisArray=[],this.basePath=e.basePath||"",this.consumes=e.consumes,this.host=e.host||"",this.info=e.info||{},this.produces=e.produces,this.schemes=e.schemes||[],this.securityDefinitions=e.securityDefinitions,this.security=e.security,this.title=e.title||"",e.externalDocs&&(this.externalDocs=e.externalDocs),this.authSchemes=e.securityDefinitions;var t,n={};if(Array.isArray(e.tags))for(n={},t=0;t<e.tags.length;t++){var i=e.tags[t];n[i.name]=i}var u;"string"==typeof this.url?(u=this.parseUri(this.url),"undefined"==typeof this.scheme&&"undefined"==typeof this.schemes||0===this.schemes.length?this.scheme=u.scheme||"http":"undefined"==typeof this.scheme&&(this.scheme=this.schemes[0]||u.scheme),"undefined"!=typeof this.host&&""!==this.host||(this.host=u.host,u.port&&(this.host=this.host+":"+u.port))):"undefined"==typeof this.schemes||0===this.schemes.length?this.scheme="http":"undefined"==typeof this.scheme&&(this.scheme=this.schemes[0]),this.definitions=e.definitions;var c;for(c in this.definitions){var p=new o(c,this.definitions[c],this.models,this.modelPropertyMacro);p&&(this.models[c]=p)}var h=this;h.apis.help=r.bind(h.help,h),r.forEach(e.paths,function(e,t){r.isPlainObject(e)&&r.forEach(m,function(i){var o=e[i];if(!r.isUndefined(o)){if(!r.isPlainObject(o))return void a.log("The '"+i+"' operation for '"+t+"' path is not an Operation Object");var u=o.tags;!r.isUndefined(u)&&r.isArray(u)&&0!==u.length||(u=o.tags=["default"]);var c=h.idFromOp(t,i,o),p=new s(h,o.scheme,c,i,t,o,h.definitions,h.models,h.clientAuthorizations);r.forEach(u,function(e){var t=r.indexOf(f,e)>-1?"_"+e:e,i=r.indexOf(d,e)>-1?"_"+e:e,o=h[t];if(t!==e&&a.log("The '"+e+"' tag conflicts with a SwaggerClient function/property name.  Use 'client."+t+"' or 'client.apis."+e+"' instead of 'client."+e+"'."),i!==e&&a.log("The '"+e+"' tag conflicts with a SwaggerClient operation function/property name.  Use 'client.apis."+i+"' instead of 'client.apis."+e+"'."),r.indexOf(d,c)>-1&&(a.log("The '"+c+"' operationId conflicts with a SwaggerClient operation function/property name.  Use 'client.apis."+i+"._"+c+"' instead of 'client.apis."+i+"."+c+"'."),c="_"+c,p.nickname=c),r.isUndefined(o)){o=h[t]=h.apis[i]={},o.operations={},o.label=i,o.apis={};var s=n[e];r.isUndefined(s)||(o.description=s.description,o.externalDocs=s.externalDocs),h[t].help=r.bind(h.help,o),h.apisArray.push(new l(e,o.description,o.externalDocs,p))}c=h.makeUniqueOperationId(c,h.apis[i]),r.isFunction(o.help)||(o.help=r.bind(h.help,o)),h.apis[i][c]=o[c]=r.bind(p.execute,p),h.apis[i][c].help=o[c].help=r.bind(p.help,p),h.apis[i][c].asCurl=o[c].asCurl=r.bind(p.asCurl,p),o.apis[c]=o.operations[c]=p;var u=r.find(h.apisArray,function(t){return t.tag===e});u&&u.operationsArray.push(p)})}})});var y=[];return r.forEach(Object.keys(n),function(e){var t;for(t in h.apisArray){var n=h.apisArray[t];n&&e===n.name&&(y.push(n),h.apisArray[t]=null)}}),r.forEach(h.apisArray,function(e){e&&y.push(e)}),h.apisArray=y,r.forEach(e.definitions,function(e,t){e.id=t.toLowerCase(),e.name=t,h.modelsArray.push(e)}),this.isBuilt=!0,this.usePromise?(this.isValid=!0,this.isBuilt=!0,this.deferredClient.resolve(this),this.deferredClient.promise):(this.success&&this.success(),this)},y.prototype.makeUniqueOperationId=function(e,t){for(var n=0,i=e;;){var a=!1;if(r.forEach(t.operations,function(e){e.nickname===i&&(a=!0)}),!a)return i;i=e+"_"+n,n++}return e},y.prototype.parseUri=function(e){var t=/^(((([^:\/#\?]+:)?(?:(\/\/)((?:(([^:@\/#\?]+)(?:\:([^:@\/#\?]+))?)@)?(([^:\/#\?\]\[]+|\[[^\/\]@#?]+\])(?:\:([0-9]+))?))?)?)?((\/?(?:[^\/\?#]+\/+)*)([^\?#]*)))?(\?[^#]+)?)(#.*)?/,n=t.exec(e);return{scheme:n[4]?n[4].replace(":",""):void 0,host:n[11],port:n[12],path:n[15]}},y.prototype.help=function(e){var t="";return this instanceof y?r.forEach(this.apis,function(e,n){r.isPlainObject(e)&&(t+="operations for the '"+n+"' tag\n",r.forEach(e.operations,function(e,n){t+="  * "+n+": "+e.summary+"\n"}))}):(this instanceof l||r.isPlainObject(this))&&(t+="operations for the '"+this.label+"' tag\n",r.forEach(this.apis,function(e,n){t+="  * "+n+": "+e.summary+"\n"})),e?t:(a.log(t),t)},y.prototype.tagFromLabel=function(e){return e},y.prototype.idFromOp=function(e,t,n){n&&n.operationId||(n=n||{},n.operationId=t+"_"+e);var r=n.operationId.replace(/[\s!@#$%^&*()_+=\[{\]};:<>|.\/?,\\'""-]/g,"_")||e.substring(1)+"_"+t;return r=r.replace(/((_){2,})/g,"_"),r=r.replace(/^(_)*/g,""),r=r.replace(/([_])*$/g,"")},y.prototype.setHost=function(e){this.host=e,this.apis&&r.forEach(this.apis,function(t){t.operations&&r.forEach(t.operations,function(t){t.host=e})})},y.prototype.setBasePath=function(e){this.basePath=e,this.apis&&r.forEach(this.apis,function(t){t.operations&&r.forEach(t.operations,function(t){t.basePath=e})})},y.prototype.setSchemes=function(e){this.schemes=e,e&&e.length>0&&this.apis&&r.forEach(this.apis,function(t){t.operations&&r.forEach(t.operations,function(t){t.scheme=e[0]})})},y.prototype.fail=function(e){return this.usePromise?(this.deferredClient.reject(e),this.deferredClient.promise):void(this.failure?this.failure(e):this.failure(e))}},{"./auth":2,"./helpers":4,"./http":5,"./resolver":6,"./spec-converter":8,"./types/model":9,"./types/operation":10,"./types/operationGroup":11,"lodash-compat/array/indexOf":49,"lodash-compat/collection/find":53,"lodash-compat/collection/forEach":54,"lodash-compat/function/bind":58,"lodash-compat/lang/cloneDeep":138,"lodash-compat/lang/isArray":140,"lodash-compat/lang/isFunction":142,"lodash-compat/lang/isObject":144,"lodash-compat/lang/isPlainObject":145,"lodash-compat/lang/isUndefined":148,q:157}],4:[function(e,t,n){(function(n){"use strict";var r={isPlainObject:e("lodash-compat/lang/isPlainObject"),indexOf:e("lodash-compat/array/indexOf")};t.exports.__bind=function(e,t){return function(){return e.apply(t,arguments)}};var i=t.exports.log=function(){console&&"test"!==n.env.NODE_ENV&&console.log(Array.prototype.slice.call(arguments)[0])};t.exports.fail=function(e){i(e)};var a=(t.exports.optionHtml=function(e,t){return'<tr><td class="optionName">'+e+":</td><td>"+t+"</td></tr>"},t.exports.resolveSchema=function(e){return r.isPlainObject(e.schema)&&(e=a(e.schema)),e});t.exports.simpleRef=function(e){return"undefined"==typeof e?null:0===e.indexOf("#/definitions/")?e.substring("#/definitions/".length):e}}).call(this,e("_process"))},{_process:12,"lodash-compat/array/indexOf":49,"lodash-compat/lang/isPlainObject":145}],5:[function(t,n,r){"use strict";var i=t("./helpers"),a=t("superagent"),o=t("js-yaml"),s={isObject:t("lodash-compat/lang/isObject")},l=function(){this.type="JQueryHttpClient"},u=function(){this.type="SuperagentHttpClient"},c=n.exports=function(){};c.prototype.execute=function(t,n){var r;r=n&&n.client?n.client:new u(n),r.opts=n||{};var i=!1;if("undefined"!=typeof window&&"undefined"!=typeof window.jQuery&&(i=!0),"options"===t.method.toLowerCase()&&"SuperagentHttpClient"===r.type&&(e("forcing jQuery as OPTIONS are not supported by SuperAgent"),t.useJQuery=!0),this.isInternetExplorer()&&(t.useJQuery===!1||!i))throw new Error("Unsupported configuration! JQuery is required but not available");(t&&t.useJQuery===!0||this.isInternetExplorer()&&i)&&(r=new l(n));var a=t.on.response,o=t.on.error,c=function(e){return n&&n.requestInterceptor&&(e=n.requestInterceptor.apply(e)),e},p=function(e){return n&&n.responseInterceptor&&(e=n.responseInterceptor.apply(e)),a(e)},h=function(e){n&&n.responseInterceptor&&(e=n.responseInterceptor.apply(e)),o(e)};return t.on.error=function(e){h(e)},t.on.response=function(e){p(e)},s.isObject(t)&&s.isObject(t.body)&&t.body.type&&"formData"===t.body.type&&n.useJQuery&&(t.contentType=!1,t.processData=!1,delete t.headers["Content-Type"]),t=c(t)||t,t.beforeSend?t.beforeSend(function(e){r.execute(e||t)}):r.execute(t),t.deferred?t.deferred.promise:t},c.prototype.isInternetExplorer=function(){var e=!1;if("undefined"!=typeof navigator&&navigator.userAgent){var t=navigator.userAgent.toLowerCase();if(-1!==t.indexOf("msie")){var n=parseInt(t.split("msie")[1]);8>=n&&(e=!0)}}return e},l.prototype.execute=function(e){var t=this.jQuery||"undefined"!=typeof window&&window.jQuery,n=e.on,r=e;if("undefined"==typeof t||t===!1)throw new Error("Unsupported configuration! JQuery is required but not available");return e.type=e.method,e.cache=e.jqueryAjaxCache,e.data=e.body,delete e.jqueryAjaxCache,delete e.useJQuery,delete e.body,e.complete=function(e){for(var t={},a=e.getAllResponseHeaders().split("\n"),s=0;s<a.length;s++){var l=a[s].trim();if(0!==l.length){var u=l.indexOf(":");if(-1!==u){var c=l.substring(0,u).trim(),p=l.substring(u+1).trim();t[c]=p}else t[l]=null}}var h={url:r.url,method:r.method,status:e.status,statusText:e.statusText,data:e.responseText,headers:t};try{var f=e.responseJSON||o.safeLoad(e.responseText);h.obj="string"==typeof f?{}:f}catch(d){i.log("unable to parse JSON/YAML content")}if(h.obj=h.obj||null,e.status>=200&&e.status<300)n.response(h);else{if(!(0===e.status||e.status>=400&&e.status<599))return n.response(h);n.error(h)}},t.support.cors=!0,t.ajax(e)},u.prototype.execute=function(e){var t=e.method.toLowerCase();"delete"===t&&(t="del");var n=e.headers||{},r=a[t](e.url);if(e.enableCookies&&r.withCredentials(),e.body)if(s.isObject(e.body)){var l=e.headers["Content-Type"]||"";if(0===l.indexOf("multipart/form-data"))if(delete n["Content-Type"],"[object FormData]"==={}.toString.apply(e.body))for(var u=e.body.keys();;){var c=u.next();if(c.done)break;var p=c.value,h=e.body.get(p);console.log({}.toString.apply(h)),"[object File]"==={}.toString.apply(h)?r.attach(p,h):r.field(p,h)}else{var f;for(var f in e.body){var h=e.body[f];r.field(f,h)}}else s.isObject(e.body)&&(e.body=JSON.stringify(e.body),r.send(e.body))}else r.send(e.body);var d;for(d in n)r.set(d,n[d]);"function"==typeof r.buffer&&r.buffer(),r.end(function(t,n){n=n||{status:0,headers:{error:"no response from server"}};var r,a={url:e.url,method:e.method,headers:n.headers};if(!t&&n.error&&(t=n.error),t&&e.on&&e.on.error){if(a.errObj=t,a.status=n?n.status:500,a.statusText=n?n.text:t.message,n.headers&&n.headers["content-type"]&&n.headers["content-type"].indexOf("application/json")>=0)try{a.obj=JSON.parse(a.statusText)}catch(s){a.obj=null}r=e.on.error}else if(n&&e.on&&e.on.response){var l;if(n.body&&Object.keys(n.body).length>0)l=n.body;else try{l=o.safeLoad(n.text),l="string"==typeof l?null:l}catch(s){i.log("cannot parse JSON/YAML content")}a.obj="object"==typeof l?l:null,a.status=n.status,a.statusText=n.text,r=e.on.response}a.data=a.statusText,r&&r(a)})}},{"./helpers":4,"js-yaml":19,"lodash-compat/lang/isObject":144,superagent:158}],6:[function(e,t,n){"use strict";var r=e("./http"),i={isObject:e("lodash-compat/lang/isObject"),cloneDeep:e("lodash-compat/lang/cloneDeep"),isArray:e("lodash-compat/lang/isArray"),isString:e("lodash-compat/lang/isString")},a=t.exports=function(){this.failedUrls=[]};a.prototype.processAllOf=function(e,t,n,r,i,a){var o,s,l;n["x-resolved-from"]=["#/definitions/"+t];var u=n.allOf;for(u.sort(function(e,t){return e.$ref&&t.$ref?0:e.$ref?-1:1}),o=0;o<u.length;o++)l=u[o],s="/definitions/"+t+"/allOf",this.resolveInline(e,a,l,r,i,s)},a.prototype.resolve=function(e,t,n,a){this.spec=e;var o,s,l=t,u=n,c=a,p={};"function"==typeof t&&(l=null,u=t,c=n);var h=l;this.scope=c||this,this.iteration=this.iteration||0,this.scope.options&&this.scope.options.requestInterceptor&&(p.requestInterceptor=this.scope.options.requestInterceptor),this.scope.options&&this.scope.options.responseInterceptor&&(p.responseInterceptor=this.scope.options.responseInterceptor);var f,d,m,y,g=0,v={},b={},w=[];e.definitions=e.definitions||{};for(f in e.definitions){var x=e.definitions[f];if(x.$ref)this.resolveInline(l,e,x,w,b,x);else{for(y in x.properties)m=x.properties[y],i.isArray(m.allOf)?this.processAllOf(l,f,m,w,b,e):this.resolveTo(l,m,w,"/definitions");x.allOf&&this.processAllOf(l,f,x,w,b,e)}}e.parameters=e.parameters||{};for(f in e.parameters){var j=e.parameters[f];if("body"===j["in"]&&j.schema)if(i.isArray(j.schema.allOf)){for(var O="inline_model",f=O,A=!1,S=0;!A;){if("undefined"==typeof e.definitions[f]){A=!0;break}f=O+"_"+S,S++}e.definitions[f]={allOf:j.schema.allOf},delete j.schema.allOf,j.schema.$ref="#/definitions/"+f,this.processAllOf(l,f,e.definitions[f],w,b,e)}else this.resolveTo(l,j.schema,w,o);j.$ref&&this.resolveInline(l,e,j,w,b,j.$ref)}for(f in e.paths){var _,k,E;d=e.paths[f];for(_ in d)if("$ref"===_)o="/paths"+f,this.resolveInline(l,e,d,w,b,o);else{k=d[_];var C=d.parameters||[],I=k.parameters||[];for(s in C){var j=C[s];I.unshift(j)}"parameters"!==_&&i.isObject(k)&&(k.parameters=k.parameters||I);for(s in I){var j=I[s];if(o="/paths"+f+"/"+_+"/parameters","body"===j["in"]&&j.schema)if(i.isArray(j.schema.allOf)){for(var O="inline_model",f=O,A=!1,S=0;!A;){if("undefined"==typeof e.definitions[f]){A=!0;break}f=O+"_"+S,S++}e.definitions[f]={allOf:j.schema.allOf},delete j.schema.allOf,j.schema.$ref="#/definitions/"+f,this.processAllOf(l,f,e.definitions[f],w,b,e)}else this.resolveTo(l,j.schema,w,o);j.$ref&&this.resolveInline(l,e,j,w,b,j.$ref)}for(E in k.responses){var T=k.responses[E];if(o="/paths"+f+"/"+_+"/responses/"+E,i.isObject(T)&&(T.$ref&&this.resolveInline(l,e,T,w,b,o),T.schema)){var M=T;if(i.isArray(M.schema.allOf)){for(var O="inline_model",f=O,A=!1,S=0;!A;){if("undefined"==typeof e.definitions[f]){A=!0;break}f=O+"_"+S,S++}e.definitions[f]={allOf:M.schema.allOf},delete M.schema.allOf,delete M.schema.type,M.schema.$ref="#/definitions/"+f,this.processAllOf(l,f,e.definitions[f],w,b,e)}else"array"===M.schema.type?M.schema.items&&M.schema.items.$ref&&this.resolveInline(l,e,M.schema.items,w,b,o):this.resolveTo(l,T.schema,w,o)}}}d.parameters=[]}var $,U=0,D=[],P=w;for(s=0;s<P.length;s++){var L=P[s];if(l===L.root){if("ref"===L.resolveAs){var R,N=((L.root||"")+"/"+L.key).split("/"),F=[],B="";if(L.key.indexOf("../")>=0){for(var q=0;q<N.length;q++)".."===N[q]?F=F.slice(0,F.length-1):F.push(N[q]);for(R=0;R<F.length;R++)R>0&&(B+="/"),B+=F[R];L.root=B,D.push(L)}else if($=L.key.split("#"),2===$.length){0!==$[0].indexOf("http:")&&0!==$[0].indexOf("https:")||(L.root=$[0]),o=$[1].split("/");var V,z=e;for(R=0;R<o.length;R++){var H=o[R];if(""!==H){if(z=z[H],"undefined"==typeof z){V=null;break}V=z}}null===V&&D.push(L)}}else if("inline"===L.resolveAs){if(L.key&&-1===L.key.indexOf("#")&&"/"!==L.key.charAt(0)){for($=L.root.split("/"),o="",s=0;s<$.length-1;s++)o+=$[s]+"/";o+=L.key,L.root=o,L.location="";
}D.push(L)}}else D.push(L)}U=D.length;for(var J=0;J<D.length;J++)!function(e,t,n){if(e.root&&e.root!==l)if(-1===n.failedUrls.indexOf(e.root)){var i={useJQuery:!1,url:e.root,method:"get",headers:{accept:n.scope.swaggerRequestHeaders||"application/json"},on:{error:function(r){g+=1,console.log("failed url: "+i.url),n.failedUrls.push(i.url),b[e.key]={root:e.root,location:e.location},g===U&&n.finish(t,h,w,v,b,u)},response:function(r){var i=r.obj;n.resolveItem(i,e.root,w,v,b,e),g+=1,g===U&&n.finish(t,h,w,v,b,u)}}};c&&c.clientAuthorizations&&c.clientAuthorizations.apply(i),(new r).execute(i,p)}else g+=1,b[e.key]={root:e.root,location:e.location},g===U&&n.finish(t,h,w,v,b,u);else n.resolveItem(t,h,w,v,b,e),g+=1,g===U&&n.finish(t,l,w,v,b,u,!0)}(D[J],e,this);0===Object.keys(D).length&&this.finish(e,h,w,v,b,u)},a.prototype.resolveItem=function(e,t,n,r,i,a){var o=a.location,s=e,l=o.split("/");if(""!==o)for(var u=0;u<l.length;u++){var c=l[u];if(-1!==c.indexOf("~1")&&(c=l[u].replace(/~0/g,"~").replace(/~1/g,"/"),"/"!==c.charAt(0)&&(c="/"+c)),"undefined"==typeof s||null===s)break;if(""===c&&u===l.length-1&&l.length>1){s=null;break}c.length>0&&(s=s[c])}var p=a.key;l=a.key.split("/");var h=l[l.length-1];h.indexOf("#")>=0&&(h=h.split("#")[1]),null!==s&&"undefined"!=typeof s?r[p]={name:h,obj:s,key:a.key,root:a.root}:i[p]={root:a.root,location:a.location}},a.prototype.finish=function(e,t,n,r,i,a,o){var s;for(s in n){var l=n[s],u=l.key,c=r[u];if(c)if(e.definitions=e.definitions||{},"ref"===l.resolveAs){if(o!==!0)for(u in c.obj){var p=this.retainRoot(u,c.obj[u],l.root);c.obj[u]=p}e.definitions[c.name]=c.obj,l.obj.$ref="#/definitions/"+c.name}else if("inline"===l.resolveAs){var h=l.obj;h["x-resolved-from"]=[l.key],delete h.$ref;for(u in c.obj){var p=c.obj[u];o!==!0&&(p=this.retainRoot(u,c.obj[u],l.root)),h[u]=p}}}var f=this.countUnresolvedRefs(e);0===f||this.iteration>5?(this.resolveAllOf(e.definitions),a.call(this.scope,e,i)):(this.iteration+=1,this.resolve(e,t,a,this.scope))},a.prototype.countUnresolvedRefs=function(e){var t,n=this.getRefs(e),r=[],i=[];for(t in n)0===t.indexOf("#")?r.push(t.substring(1)):i.push(t);for(t=0;t<r.length;t++)for(var a=r[t],o=a.split("/"),s=e,l=0;l<o.length;l++){var u=o[l];if(""!==u&&(s=s[u],"undefined"==typeof s)){i.push(a);break}}return i.length},a.prototype.getRefs=function(e,t){t=t||e;var n={};for(var r in t)if(t.hasOwnProperty(r)){var a=t[r];if("$ref"===r&&"string"==typeof a)n[a]=null;else if(i.isObject(a)){var o=this.getRefs(a);for(var s in o)n[s]=null}}return n},a.prototype.retainRoot=function(e,t,n){if(i.isObject(t))for(var r in t){var a=t[r];if("$ref"===r&&"string"==typeof a){if(0!==a.indexOf("http:")&&0!==a.indexOf("https:")){var o=!0;if(n){var s=n.slice(-1);if("/"!==s&&0!==a.indexOf("#")&&0!==a.indexOf("http:")&&a.indexOf("https:")){o=!1;var l=n.split("/");l=l.splice(0,l.length-1),n="";for(var u=0;u<l.length;u++)n+=l[u]+"/"}}0!==a.indexOf("#")&&o&&(a="#"+a),a=(n||"")+a,t[r]=a}}else i.isObject(a)&&this.retainRoot(r,a,n)}else i.isString(t)&&"$ref"===e&&-1===t.indexOf("http:")&&-1===t.indexOf("https:")&&(t=n+t);return t},a.prototype.resolveInline=function(e,t,n,r,i,a){var o,s,l,u,c=n.$ref,p=n.$ref,h=!1;if(e=e||"",p){if(0===p.indexOf("../")){for(s=p.split("../"),l=e.split("/"),p="",o=0;o<s.length;o++)""===s[o]?l=l.slice(0,l.length-1):p+=s[o];for(e="",o=0;o<l.length-1;o++)o>0&&(e+="/"),e+=l[o];h=!0}if(p.indexOf("#")>=0)if(0===p.indexOf("/"))u=p.split("#"),s=e.split("//"),l=s[1].split("/"),e=s[0]+"//"+l[0]+u[0],a=u[1];else{if(u=p.split("#"),""!==u[0]){if(l=e.split("/"),l=l.slice(0,l.length-1),!h){e="";for(var f=0;f<l.length;f++)f>0&&(e+="/"),e+=l[f]}e+="/"+p.split("#")[0]}a=u[1]}if(0===p.indexOf("http:")||0===p.indexOf("https:"))p.indexOf("#")>=0?(e=p.split("#")[0],a=p.split("#")[1]):(e=p,a=""),r.push({obj:n,resolveAs:"inline",root:e,key:c,location:a});else if(0===p.indexOf("#"))a=p.split("#")[1],r.push({obj:n,resolveAs:"inline",root:e,key:c,location:a});else if(0===p.indexOf("/")&&-1===p.indexOf("#")){a=p;var d=e.match(/^https?\:\/\/([^\/?#]+)(?:[\/?#]|$)/i);d&&(e=d[0]+p.substring(1),a=""),r.push({obj:n,resolveAs:"inline",root:e,key:c,location:a})}else r.push({obj:n,resolveAs:"inline",root:e,key:c,location:a})}else"array"===n.type&&this.resolveTo(e,n.items,r,a)},a.prototype.resolveTo=function(e,t,n,r){var a,o,s=t.$ref,l=e;if("undefined"!=typeof s&&null!==s){if(s.indexOf("#")>=0){var u=s.split("#");if(u[0]&&0===s.indexOf("/"));else if(!u[0]||0!==u[0].indexOf("http:")&&0!==u[0].indexOf("https:")){if(u[0]&&u[0].length>0){for(a=e.split("/"),l="",o=0;o<a.length-1;o++)l+=a[o]+"/";l+=u[0]}}else l=u[0],s=u[1];r=u[1]}else if(0===s.indexOf("http:")||0===s.indexOf("https:"))l=s,r="";else{for(a=e.split("/"),l="",o=0;o<a.length-1;o++)l+=a[o]+"/";l+=s,r=""}n.push({obj:t,resolveAs:"ref",root:l,key:s,location:r})}else if("array"===t.type){var c=t.items;this.resolveTo(e,c,n,r)}else if(t&&t.properties){var p=this.uniqueName("inline_model");t.title&&(p=this.uniqueName(t.title)),delete t.title,this.spec.definitions[p]=i.cloneDeep(t),t.$ref="#/definitions/"+p,delete t.type,delete t.properties}},a.prototype.uniqueName=function(e){for(var t=e,n=0;;){if(!i.isObject(this.spec.definitions[t]))return t;t=e+"_"+n,n++}},a.prototype.resolveAllOf=function(e,t,n){n=n||0,t=t||e;var r;for(var a in t)if(t.hasOwnProperty(a)){var o=t[a];if(null===o)throw new TypeError("Swagger 2.0 does not support null types ("+t+").  See https://github.com/swagger-api/swagger-spec/issues/229.");if("object"==typeof o&&this.resolveAllOf(e,o,n+1),o&&"undefined"!=typeof o.allOf){var s=o.allOf;if(i.isArray(s)){var l=i.cloneDeep(o);delete l.allOf,l["x-composed"]=!0,"undefined"!=typeof o["x-resolved-from"]&&(l["x-resolved-from"]=o["x-resolved-from"]);for(var u=0;u<s.length;u++){var c=s[u],p="self";"undefined"!=typeof c["x-resolved-from"]&&(p=c["x-resolved-from"][0]);for(var h in c)if(l.hasOwnProperty(h))if("properties"===h){var f=c[h];for(r in f){l.properties[r]=i.cloneDeep(f[r]);var d=f[r]["x-resolved-from"];"undefined"!=typeof d&&"self"!==d||(d=p),l.properties[r]["x-resolved-from"]=d}}else if("required"===h){for(var m=l.required.concat(c[h]),y=0;y<m.length;++y)for(var g=y+1;g<m.length;++g)m[y]===m[g]&&m.splice(g--,1);l.required=m}else"x-resolved-from"===h&&l["x-resolved-from"].push(p);else if(l[h]=i.cloneDeep(c[h]),"properties"===h)for(r in l[h])l[h][r]["x-resolved-from"]=p}t[a]=l}}}}},{"./http":5,"lodash-compat/lang/cloneDeep":138,"lodash-compat/lang/isArray":140,"lodash-compat/lang/isObject":144,"lodash-compat/lang/isString":146}],7:[function(e,t,n){"use strict";function r(e,t){return'<tr><td class="optionName">'+e+":</td><td>"+t+"</td></tr>"}function i(e,t){var n;return"integer"===e&&"int32"===t?n="integer":"integer"===e&&"int64"===t?n="long":"integer"===e&&"undefined"==typeof t?n="long":"string"===e&&"date-time"===t?n="date-time":"string"===e&&"date"===t?n="date":"number"===e&&"float"===t?n="float":"number"===e&&"double"===t?n="double":"number"===e&&"undefined"==typeof t?n="double":"boolean"===e?n="boolean":"string"===e&&(n="string"),n}function a(e,t){var n="";return"undefined"!=typeof e.$ref?n+=l.simpleRef(e.$ref):"undefined"==typeof e.type?n+="object":"array"===e.type?t?n+=a(e.items||e.$ref||{}):(n+="Array[",n+=a(e.items||e.$ref||{}),n+="]"):n+="integer"===e.type&&"int32"===e.format?"integer":"integer"===e.type&&"int64"===e.format?"long":"integer"===e.type&&"undefined"==typeof e.format?"long":"string"===e.type&&"date-time"===e.format?"date-time":"string"===e.type&&"date"===e.format?"date":"string"===e.type&&"undefined"==typeof e.format?"string":"number"===e.type&&"float"===e.format?"float":"number"===e.type&&"double"===e.format?"double":"number"===e.type&&"undefined"==typeof e.format?"double":"boolean"===e.type?"boolean":e.$ref?l.simpleRef(e.$ref):e.type,n}function o(e,t,n,r){e=l.resolveSchema(e),"function"!=typeof r&&(r=function(e){return(e||{})["default"]}),n=n||{};var i,a,s=e.type||"object",c=e.format;return u.isUndefined(e.example)?u.isUndefined(e.items)&&u.isArray(e["enum"])&&(a=e["enum"][0]):a=e.example,u.isUndefined(a)&&(e.$ref?(i=t[l.simpleRef(e.$ref)],u.isUndefined(i)||(u.isUndefined(n[i.name])?(n[i.name]=i,a=o(i.definition,t,n,r),delete n[i.name]):a="array"===i.type?[]:{})):u.isUndefined(e["default"])?"string"===s?a="date-time"===c?(new Date).toISOString():"date"===c?(new Date).toISOString().split("T")[0]:"string":"integer"===s?a=0:"number"===s?a=0:"boolean"===s?a=!0:"object"===s?(a={},u.forEach(e.properties,function(e,i){var s=u.cloneDeep(e);s["default"]=r(e),a[i]=o(s,t,n,r)})):"array"===s&&(a=[],u.isArray(e.items)?u.forEach(e.items,function(e){a.push(o(e,t,n,r))}):u.isPlainObject(e.items)?a.push(o(e.items,t,n,r)):u.isUndefined(e.items)?a.push({}):l.log("Array type's 'items' property is not an array or an object, cannot process")):a=e["default"]),a}function s(e,t,n,i){function a(e,t,r){var i,a=t;return e.$ref?(a=e.title||l.simpleRef(e.$ref),i=n[a]):u.isUndefined(t)&&(a=e.title||"Inline Model "+ ++m,i={definition:e}),r!==!0&&(f[a]=u.isUndefined(i)?{}:i.definition),a}function o(e){var t='<span class="propType">',n=e.type||"object";return e.$ref?t+=a(e,l.simpleRef(e.$ref)):"object"===n?t+=u.isUndefined(e.properties)?"object":a(e):"array"===n?(t+="Array[",u.isArray(e.items)?t+=u.map(e.items,a).join(","):u.isPlainObject(e.items)?t+=u.isUndefined(e.items.$ref)?u.isUndefined(e.items.type)||-1!==u.indexOf(["array","object"],e.items.type)?a(e.items):e.items.type:a(e.items,l.simpleRef(e.items.$ref)):(l.log("Array type's 'items' schema is not an array or an object, cannot process"),t+="object"),t+="]"):t+=e.type,t+="</span>"}function s(e,t){var n="",i=e.type||"object",a="array"===i;switch(a&&(i=u.isPlainObject(e.items)&&!u.isUndefined(e.items.type)?e.items.type:"object"),u.isUndefined(e["default"])||(n+=r("Default",e["default"])),i){case"string":e.minLength&&(n+=r("Min. Length",e.minLength)),e.maxLength&&(n+=r("Max. Length",e.maxLength)),e.pattern&&(n+=r("Reg. Exp.",e.pattern));break;case"integer":case"number":e.minimum&&(n+=r("Min. Value",e.minimum)),e.exclusiveMinimum&&(n+=r("Exclusive Min.","true")),e.maximum&&(n+=r("Max. Value",e.maximum)),e.exclusiveMaximum&&(n+=r("Exclusive Max.","true")),e.multipleOf&&(n+=r("Multiple Of",e.multipleOf))}if(a&&(e.minItems&&(n+=r("Min. Items",e.minItems)),e.maxItems&&(n+=r("Max. Items",e.maxItems)),e.uniqueItems&&(n+=r("Unique Items","true")),e.collectionFormat&&(n+=r("Coll. Format",e.collectionFormat))),u.isUndefined(e.items)&&u.isArray(e["enum"])){var o;o="number"===i||"integer"===i?e["enum"].join(", "):'"'+e["enum"].join('", "')+'"',n+=r("Enum",o)}return n.length>0&&(t='<span class="propWrap">'+t+'<table class="optionsWrapper"><tr><th colspan="2">'+i+"</th></tr>"+n+"</table></span>"),t}function c(e,t){var r=e.type||"object",c="array"===e.type,f=p+t+" "+(c?"[":"{")+h;if(t&&d.push(t),c)u.isArray(e.items)?f+="<div>"+u.map(e.items,function(e){var t=e.type||"object";return u.isUndefined(e.$ref)?u.indexOf(["array","object"],t)>-1?"object"===t&&u.isUndefined(e.properties)?"object":a(e):s(e,t):a(e,l.simpleRef(e.$ref))}).join(",</div><div>"):u.isPlainObject(e.items)?f+=u.isUndefined(e.items.$ref)?u.indexOf(["array","object"],e.items.type||"object")>-1?(u.isUndefined(e.items.type)||"object"===e.items.type)&&u.isUndefined(e.items.properties)?"<div>object</div>":"<div>"+a(e.items)+"</div>":"<div>"+s(e.items,e.items.type)+"</div>":"<div>"+a(e.items,l.simpleRef(e.items.$ref))+"</div>":(l.log("Array type's 'items' property is not an array or an object, cannot process"),f+="<div>object</div>");else if(e.$ref)f+="<div>"+a(e,t)+"</div>";else if("object"===r){if(u.isPlainObject(e.properties)){var m=u.map(e.properties,function(t,r){var a,c,p=u.indexOf(e.required,r)>=0,h=u.cloneDeep(t),f=p?"required":"",d='<span class="propName '+f+'">'+r+"</span> (";return h["default"]=i(h),h=l.resolveSchema(h),c=t.description||h.description,u.isUndefined(h.$ref)||(a=n[l.simpleRef(h.$ref)],u.isUndefined(a)||-1!==u.indexOf([void 0,"array","object"],a.definition.type)||(h=l.resolveSchema(a.definition))),d+=o(h),p||(d+=', <span class="propOptKey">optional</span>'),t.readOnly&&(d+=', <span class="propReadOnly">read only</span>'),d+=")",u.isUndefined(c)||(d+=': <span class="propDesc">'+c+"</span>"),h["enum"]&&(d+=' = <span class="propVals">[\''+h["enum"].join("', '")+"']</span>"),"<div"+(t.readOnly?' class="readOnly"':"")+">"+s(h,d)}).join(",</div>");m&&(f+=m+"</div>")}}else f+="<div>"+s(e,r)+"</div>";return f+p+(c?"]":"}")+h}var p='<span class="strong">',h="</span>";if(u.isObject(arguments[0])&&(e=void 0,t=arguments[0],n=arguments[1],i=arguments[2]),n=n||{},t=l.resolveSchema(t),u.isEmpty(t))return p+"Empty"+h;if("string"==typeof t.$ref&&(e=l.simpleRef(t.$ref),t=n[e],"undefined"==typeof t))return p+e+" is not defined!"+h;"string"!=typeof e&&(e=t.title||"Inline Model"),t.definition&&(t=t.definition),"function"!=typeof i&&(i=function(e){return(e||{})["default"]});for(var f={},d=[],m=0,y=c(t,e);u.keys(f).length>0;)u.forEach(f,function(e,t){var n=u.indexOf(d,t)>-1;delete f[t],n||(d.push(t),y+="<br />"+c(e,t))});return y}var l=e("./helpers"),u={isPlainObject:e("lodash-compat/lang/isPlainObject"),isUndefined:e("lodash-compat/lang/isUndefined"),isArray:e("lodash-compat/lang/isArray"),isObject:e("lodash-compat/lang/isObject"),isEmpty:e("lodash-compat/lang/isEmpty"),map:e("lodash-compat/collection/map"),indexOf:e("lodash-compat/array/indexOf"),cloneDeep:e("lodash-compat/lang/cloneDeep"),keys:e("lodash-compat/object/keys"),forEach:e("lodash-compat/collection/forEach")};t.exports.optionHtml=r,t.exports.typeFromJsonSchema=i,t.exports.getStringSignature=a,t.exports.schemaToHTML=s,t.exports.schemaToJSON=o},{"./helpers":4,"lodash-compat/array/indexOf":49,"lodash-compat/collection/forEach":54,"lodash-compat/collection/map":56,"lodash-compat/lang/cloneDeep":138,"lodash-compat/lang/isArray":140,"lodash-compat/lang/isEmpty":141,"lodash-compat/lang/isObject":144,"lodash-compat/lang/isPlainObject":145,"lodash-compat/lang/isUndefined":148,"lodash-compat/object/keys":149}],8:[function(e,t,n){"use strict";var r=e("./http"),i={isObject:e("lodash-compat/lang/isObject")},a=t.exports=function(){this.errors=[],this.warnings=[],this.modelMap={}};a.prototype.setDocumentationLocation=function(e){this.docLocation=e},a.prototype.convert=function(e,t,n,r){if(!e||!Array.isArray(e.apis))return this.finish(r,null);this.clientAuthorizations=t;var i={swagger:"2.0"};i.originalVersion=e.swaggerVersion,this.apiInfo(e,i),this.securityDefinitions(e,i),e.basePath&&this.setDocumentationLocation(e.basePath);var a,o=!1;for(a=0;a<e.apis.length;a++){var s=e.apis[a];Array.isArray(s.operations)&&(o=!0)}o?(this.declaration(e,i),this.finish(r,i)):this.resourceListing(e,i,n,r)},a.prototype.declaration=function(e,t){var n,r,a,o;if(e.apis){0===e.basePath.indexOf("http://")?(a=e.basePath.substring("http://".length),o=a.indexOf("/"),o>0?(t.host=a.substring(0,o),t.basePath=a.substring(o)):(t.host=a,t.basePath="/")):0===e.basePath.indexOf("https://")?(a=e.basePath.substring("https://".length),o=a.indexOf("/"),o>0?(t.host=a.substring(0,o),t.basePath=a.substring(o)):(t.host=a,t.basePath="/")):t.basePath=e.basePath;var s;if(e.authorizations&&(s=e.authorizations),e.consumes&&(t.consumes=e.consumes),e.produces&&(t.produces=e.produces),i.isObject(e))for(n in e.models){var l=e.models[n],u=l.id||n;this.modelMap[u]=n}for(r=0;r<e.apis.length;r++){var c=e.apis[r],p=c.path,h=c.operations;this.operations(p,e.resourcePath,h,s,t)}var f=e.models||{};this.models(f,t)}},a.prototype.models=function(e,t){if(i.isObject(e)){var n;t.definitions=t.definitions||{};for(n in e){var r,a=e[n],o=[],s={properties:{}};for(r in a.properties){var l=a.properties[r],u={};this.dataType(l,u),l.description&&(u.description=l.description),l["enum"]&&(u["enum"]=l["enum"]),"boolean"==typeof l.required&&l.required===!0&&o.push(r),"string"==typeof l.required&&"true"===l.required&&o.push(r),s.properties[r]=u}o.length>0?s.required=o:s.required=a.required,t.definitions[n]=s}}},a.prototype.extractTag=function(e){var t=e||"default";return 0!==t.indexOf("http:")&&0!==t.indexOf("https:")||(t=t.split(["/"]),t=t[t.length-1].substring()),t.endsWith(".json")&&(t=t.substring(0,t.length-".json".length)),t.replace("/","")},a.prototype.operations=function(e,t,n,r,i){if(Array.isArray(n)){var a;i.paths||(i.paths={});var o=i.paths[e]||{},s=this.extractTag(t);i.tags=i.tags||[];var l=!1;for(a=0;a<i.tags.length;a++){var u=i.tags[a];u.name===s&&(l=!0)}for(l||i.tags.push({name:s}),a=0;a<n.length;a++){var c=n[a],p=(c.method||c.httpMethod).toLowerCase(),h={tags:[s]},f=c.authorizations;if(f&&0===Object.keys(f).length&&(f=r),"undefined"!=typeof f){var d;for(var m in f){h.security=h.security||[];var y=f[m];if(y){var g=[];for(var v in y)g.push(y[v].scope);d={},d[m]=g,h.security.push(d)}else d={},d[m]=[],h.security.push(d)}}c.consumes?h.consumes=c.consumes:i.consumes&&(h.consumes=i.consumes),c.produces?h.produces=c.produces:i.produces&&(h.produces=i.produces),c.summary&&(h.summary=c.summary),c.notes&&(h.description=c.notes),c.nickname&&(h.operationId=c.nickname),c.deprecated&&(h.deprecated=c.deprecated),this.authorizations(f,i),this.parameters(h,c.parameters,i),this.responseMessages(h,c,i),o[p]=h}i.paths[e]=o}},a.prototype.responseMessages=function(e,t){if(i.isObject(t)){var n={};this.dataType(t,n),!n.schema&&n.type&&(n={schema:n}),e.responses=e.responses||{};var r=!1;if(Array.isArray(t.responseMessages)){var a,o=t.responseMessages;for(a=0;a<o.length;a++){var s=o[a],l={description:s.message};200===s.code&&(r=!0),s.responseModel&&(l.schema={$ref:"#/definitions/"+s.responseModel}),e.responses[""+s.code]=l}}r?e.responses["default"]=n:e.responses[200]=n}},a.prototype.authorizations=function(e){!i.isObject(e)},a.prototype.parameters=function(e,t){if(Array.isArray(t)){var n;for(n=0;n<t.length;n++){var r=t[n],i={};if(i.name=r.name,i.description=r.description,i.required=r.required,i["in"]=r.paramType,"body"===i["in"]&&(i.name="body"),"form"===i["in"]&&(i["in"]="formData"),r["enum"]&&(i["enum"]=r["enum"]),r.allowMultiple===!0||"true"===r.allowMultiple){var a={};if(this.dataType(r,a),i.type="array",i.items=a,r.allowableValues){var o=r.allowableValues;"LIST"===o.valueType&&(i["enum"]=o.values)}}else this.dataType(r,i);"undefined"!=typeof r.defaultValue&&(i["default"]=r.defaultValue),e.parameters=e.parameters||[],e.parameters.push(i)}}},a.prototype.dataType=function(e,t){if(i.isObject(e)){e.minimum&&(t.minimum=e.minimum),e.maximum&&(t.maximum=e.maximum),e.format&&(t.format=e.format),"undefined"!=typeof e.defaultValue&&(t["default"]=e.defaultValue);var n=this.toJsonSchema(e);n&&(t=t||{},n.type&&(t.type=n.type),n.format&&(t.format=n.format),n.$ref&&(t.schema={$ref:n.$ref}),n.items&&(t.items=n.items))}},a.prototype.toJsonSchema=function(e){if(!e)return"object";var t=e.type||e.dataType||e.responseClass||"",n=t.toLowerCase(),r=(e.format||"").toLowerCase();if(0===n.indexOf("list[")){var i=t.substring(5,t.length-1),a=this.toJsonSchema({type:i});return{type:"array",items:a}}if("int"===n||"integer"===n&&"int32"===r)return{type:"integer",format:"int32"};if("long"===n||"integer"===n&&"int64"===r)return{type:"integer",format:"int64"};if("integer"===n)return{type:"integer",format:"int64"};if("float"===n||"number"===n&&"float"===r)return{type:"number",format:"float"};if("double"===n||"number"===n&&"double"===r)return{type:"number",format:"double"};if("string"===n&&"date-time"===r||"date"===n)return{type:"string",format:"date-time"};if("string"===n)return{type:"string"};if("file"===n)return{type:"file"};if("boolean"===n)return{type:"boolean"};if("boolean"===n)return{type:"boolean"};if("array"===n||"list"===n){if(e.items){var o=this.toJsonSchema(e.items);return{type:"array",items:o}}return{type:"array",items:{type:"object"}}}return e.$ref?{$ref:this.modelMap[e.$ref]?"#/definitions/"+this.modelMap[e.$ref]:e.$ref}:"void"===n||""===n?{}:this.modelMap[e.type]?{$ref:"#/definitions/"+this.modelMap[e.type]}:{type:e.type}},a.prototype.resourceListing=function(e,t,n,i){var a,o=0,s=this,l=e.apis.length,u=t,c={};n&&n.requestInterceptor&&(c.requestInterceptor=n.requestInterceptor),n&&n.responseInterceptor&&(c.responseInterceptor=n.responseInterceptor);var p="application/json";for(n&&n.swaggerRequestHeaders&&(p=n.swaggerRequestHeaders),0===l&&this.finish(i,t),a=0;l>a;a++){var h=e.apis[a],f=h.path,d=this.getAbsolutePath(e.swaggerVersion,this.docLocation,f);h.description&&(t.tags=t.tags||[],t.tags.push({name:this.extractTag(h.path),description:h.description||""}));var m={url:d,headers:{accept:p},on:{},method:"get"};m.on.response=function(e){o+=1;var t=e.obj;t&&s.declaration(t,u),o===l&&s.finish(i,u)},m.on.error=function(e){console.error(e),o+=1,o===l&&s.finish(i,u)},this.clientAuthorizations&&"function"==typeof this.clientAuthorizations.apply&&this.clientAuthorizations.apply(m),(new r).execute(m,c)}},a.prototype.getAbsolutePath=function(e,t,n){if("1.0"===e&&t.endsWith(".json")){var r=t.lastIndexOf("/");r>0&&(t=t.substring(0,r))}var i=t;return 0===n.indexOf("http:")||0===n.indexOf("https:")?i=n:(t.endsWith("/")&&(i=t.substring(0,t.length-1)),i+=n),i=i.replace("{format}","json")},a.prototype.securityDefinitions=function(e,t){if(e.authorizations){var n;for(n in e.authorizations){var r=!1,i={},a=e.authorizations[n];if("apiKey"===a.type)i.type="apiKey",i["in"]=a.passAs,i.name=a.keyname||n,r=!0;else if("basicAuth"===a.type)i.type="basicAuth",r=!0;else if("oauth2"===a.type){var o,s=a.scopes||[],l={};for(o in s){var u=s[o];l[u.scope]=u.description}if(i.type="oauth2",o>0&&(i.scopes=l),a.grantTypes){if(a.grantTypes.implicit){var c=a.grantTypes.implicit;i.flow="implicit",i.authorizationUrl=c.loginEndpoint,r=!0}if(a.grantTypes.authorization_code&&!i.flow){var p=a.grantTypes.authorization_code;i.flow="accessCode",i.authorizationUrl=p.tokenRequestEndpoint.url,i.tokenUrl=p.tokenEndpoint.url,r=!0}}}r&&(t.securityDefinitions=t.securityDefinitions||{},t.securityDefinitions[n]=i)}}},a.prototype.apiInfo=function(e,t){if(e.info){var n=e.info;t.info={},n.contact&&(t.info.contact={},t.info.contact.email=n.contact),n.description&&(t.info.description=n.description),n.title&&(t.info.title=n.title),n.termsOfServiceUrl&&(t.info.termsOfService=n.termsOfServiceUrl),(n.license||n.licenseUrl)&&(t.license={},n.license&&(t.license.name=n.license),n.licenseUrl&&(t.license.url=n.licenseUrl))}else this.warnings.push("missing info section")},a.prototype.finish=function(e,t){e(t)}},{"./http":5,"lodash-compat/lang/isObject":144}],9:[function(e,t,n){"use strict";var r=(e("../helpers").log,{isPlainObject:e("lodash-compat/lang/isPlainObject"),isString:e("lodash-compat/lang/isString")}),i=e("../schema-markup.js"),a=e("js-yaml"),o=t.exports=function(e,t,n,r){return this.definition=t||{},this.isArray="array"===t.type,this.models=n||{},this.name=e||t.title||"Inline Model",this.modelPropertyMacro=r||function(e){return e["default"]},this};o.prototype.createJSONSample=o.prototype.getSampleValue=function(e){return e=e||{},e[this.name]=this,this.examples&&r.isPlainObject(this.examples)&&this.examples["application/json"]?(this.definition.example=this.examples["application/json"],r.isString(this.definition.example)&&(this.definition.example=a.safeLoad(this.definition.example))):this.definition.example||(this.definition.example=this.examples),i.schemaToJSON(this.definition,this.models,e,this.modelPropertyMacro)},o.prototype.getMockSignature=function(){return i.schemaToHTML(this.name,this.definition,this.models,this.modelPropertyMacro)}},{"../helpers":4,"../schema-markup.js":7,"js-yaml":19,"lodash-compat/lang/isPlainObject":145,"lodash-compat/lang/isString":146}],10:[function(e,t,n){"use strict";function r(e,t){if(i.isEmpty(t))return e[0];for(var n=0,r=t.length;r>n;n++)if(e.indexOf(t[n])>-1)return t[n];return e[0]}var i={cloneDeep:e("lodash-compat/lang/cloneDeep"),isUndefined:e("lodash-compat/lang/isUndefined"),isEmpty:e("lodash-compat/lang/isEmpty"),isObject:e("lodash-compat/lang/isObject")},a=e("../helpers"),o=e("./model"),s=e("../http"),l=e("q"),u=t.exports=function(e,t,n,r,i,a,s,l,u){var c=[];if(e=e||{},a=a||{},e&&e.options&&(this.client=e.options.client||null,this.requestInterceptor=e.options.requestInterceptor||null,this.responseInterceptor=e.options.responseInterceptor||null),this.authorizations=a.security,this.basePath=e.basePath||"/",this.clientAuthorizations=u,this.consumes=a.consumes||e.consumes||["application/json"],this.produces=a.produces||e.produces||["application/json"],this.deprecated=a.deprecated,this.description=a.description,this.host=e.host||"localhost",this.method=r||c.push("Operation "+n+" is missing method."),this.models=l||{},this.nickname=n||c.push("Operations must have a nickname."),this.operation=a,this.operations={},this.parameters=null!==a?a.parameters||[]:{},this.parent=e,this.path=i||c.push("Operation "+this.nickname+" is missing path."),this.responses=a.responses||{},this.scheme=t||e.scheme||"http",this.schemes=a.schemes||e.schemes,this.security=a.security||e.security,this.summary=a.summary||"",this.type=null,this.useJQuery=e.useJQuery,this.jqueryAjaxCache=e.jqueryAjaxCache,this.enableCookies=e.enableCookies,this.parameterMacro=e.parameterMacro||function(e,t){return t["default"]},this.inlineModels=[],"/"!==this.basePath&&"/"===this.basePath.slice(-1)&&(this.basePath=this.basePath.slice(0,-1)),"string"==typeof this.deprecated)switch(this.deprecated.toLowerCase()){case"true":case"yes":case"1":this.deprecated=!0;break;case"false":case"no":case"0":case null:this.deprecated=!1;break;default:this.deprecated=Boolean(this.deprecated)}var p,h;if(s){var f;for(f in s)h=new o(f,s[f],this.models,e.modelPropertyMacro),h&&(this.models[f]=h)}else s={};for(p=0;p<this.parameters.length;p++){var d=this.parameters[p];d["default"]=this.parameterMacro(this,d),"array"===d.type&&(d.isList=!0,d.allowMultiple=!0);var m=this.getType(d);if(m&&"boolean"===m.toString().toLowerCase()&&(d.allowableValues={},d.isList=!0,d["enum"]=[!0,!1]),"undefined"!=typeof d["x-example"]){var y=d["x-example"];d["default"]=y}if(d["x-examples"]){var y=d["x-examples"]["default"];"undefined"!=typeof y&&(d["default"]=y)}var g=d["enum"]||d.items&&d.items["enum"];if("undefined"!=typeof g){var v;for(d.allowableValues={},d.allowableValues.values=[],d.allowableValues.descriptiveValues=[],v=0;v<g.length;v++){var b=g[v],w=b===d["default"]||b+""===d["default"];d.allowableValues.values.push(b),d.allowableValues.descriptiveValues.push({value:b+"",isDefault:w})}}"array"===d.type&&(m=[m],"undefined"==typeof d.allowableValues&&(delete d.isList,delete d.allowMultiple)),d.modelSignature={type:m,definitions:this.models},d.signature=this.getModelSignature(m,this.models).toString(),d.sampleJSON=this.getModelSampleJSON(m,this.models),d.responseClassSignature=d.signature}var x,j,O=this.responses;if(O[200]?(j=O[200],x="200"):O[201]?(j=O[201],x="201"):O[202]?(j=O[202],x="202"):O[203]?(j=O[203],x="203"):O[204]?(j=O[204],x="204"):O[205]?(j=O[205],x="205"):O[206]?(j=O[206],x="206"):O["default"]&&(j=O["default"],x="default"),j&&j.schema){var A,S=this.resolveModel(j.schema,s);delete O[x],S?(this.successResponse={},A=this.successResponse[x]=S):j.schema.type&&"object"!==j.schema.type&&"array"!==j.schema.type?(this.successResponse={},A=this.successResponse[x]=j.schema):(this.successResponse={},A=this.successResponse[x]=new o(void 0,j.schema||{},this.models,e.modelPropertyMacro)),A&&(j.description&&(A.description=j.description),j.examples&&(A.examples=j.examples),j.headers&&(A.headers=j.headers)),this.type=j}return c.length>0&&this.resource&&this.resource.api&&this.resource.api.fail&&this.resource.api.fail(c),this};u.prototype.isDefaultArrayItemValue=function(e,t){return t["default"]&&Array.isArray(t["default"])?-1!==t["default"].indexOf(e):e===t["default"]},u.prototype.getType=function(e){var t,n=e.type,r=e.format,i=!1;"integer"===n&&"int32"===r?t="integer":"integer"===n&&"int64"===r?t="long":"integer"===n?t="integer":"string"===n?t="date-time"===r?"date-time":"date"===r?"date":"string":"number"===n&&"float"===r?t="float":"number"===n&&"double"===r?t="double":"number"===n?t="double":"boolean"===n?t="boolean":"array"===n?(i=!0,e.items&&(t=this.getType(e.items))):"file"===n&&(t="file"),e.$ref&&(t=a.simpleRef(e.$ref));var o=e.schema;if(o){var s=o.$ref;return s?(s=a.simpleRef(s),i?[s]:s):"object"===o.type?this.addInlineModel(o):this.getType(o)}return i?[t]:t},u.prototype.addInlineModel=function(e){var t=this.inlineModels.length,n=this.resolveModel(e,{});return n?(this.inlineModels.push(n),"Inline Model "+t):null},u.prototype.getInlineModel=function(e){if(/^Inline Model \d+$/.test(e)){var t=parseInt(e.substr("Inline Model".length).trim(),10),n=this.inlineModels[t];return n}return null},u.prototype.resolveModel=function(e,t){if("undefined"!=typeof e.$ref){var n=e.$ref;if(0===n.indexOf("#/definitions/")&&(n=n.substring("#/definitions/".length)),t[n])return new o(n,t[n],this.models,this.parent.modelPropertyMacro)}else if(e&&"object"==typeof e&&("object"===e.type||i.isUndefined(e.type)))return new o(void 0,e,this.models,this.parent.modelPropertyMacro);return null},u.prototype.help=function(e){for(var t=this.nickname+": "+this.summary+"\n",n=0;n<this.parameters.length;n++){var r=this.parameters[n],i=r.signature;t+="\n  * "+r.name+" ("+i+"): "+r.description}return"undefined"==typeof e&&a.log(t),t},u.prototype.getModelSignature=function(e,t){var n,r;return e instanceof Array&&(r=!0,e=e[0]),"undefined"==typeof e?(e="undefined",n=!0):t[e]?(e=t[e],n=!1):this.getInlineModel(e)?(e=this.getInlineModel(e),n=!1):n=!0,n?r?"Array["+e+"]":e.toString():r?"Array["+e.getMockSignature()+"]":e.getMockSignature()},u.prototype.supportHeaderParams=function(){return!0},u.prototype.supportedSubmitMethods=function(){return this.parent.supportedSubmitMethods},u.prototype.getHeaderParams=function(e){for(var t=this.setContentTypes(e,{}),n=0;n<this.parameters.length;n++){var r=this.parameters[n];if("undefined"!=typeof e[r.name]&&"header"===r["in"]){var i=e[r.name];Array.isArray(i)&&(i=i.toString()),t[r.name]=i}}return t},u.prototype.urlify=function(e){for(var t={},n=this.path.replace(/#.*/,""),r="",i=0;i<this.parameters.length;i++){var a=this.parameters[i];if("undefined"!=typeof e[a.name])if("path"===a["in"]){var o=new RegExp("{"+a.name+"}","gi"),s=e[a.name];s=Array.isArray(s)?this.encodePathCollection(a.collectionFormat,a.name,s):this.encodePathParam(s),n=n.replace(o,s)}else if("query"===a["in"]&&"undefined"!=typeof e[a.name])if(r+=""===r&&n.indexOf("?")<0?"?":"&","undefined"!=typeof a.collectionFormat){var l=e[a.name];r+=Array.isArray(l)?this.encodeQueryCollection(a.collectionFormat,a.name,l):this.encodeQueryKey(a.name)+"="+this.encodeQueryParam(e[a.name])}else r+=this.encodeQueryKey(a.name)+"="+this.encodeQueryParam(e[a.name]);else"formData"===a["in"]&&(t[a.name]=e[a.name])}var u=this.scheme+"://"+this.host;return"/"!==this.basePath&&(u+=this.basePath),u+n+r},u.prototype.getMissingParams=function(e){var t,n=[];for(t=0;t<this.parameters.length;t++){var r=this.parameters[t];r.required===!0&&"undefined"==typeof e[r.name]&&(n=r.name)}return n},u.prototype.getBody=function(e,t,n){for(var r,i,a,o,s={},l=!1,u=0;u<this.parameters.length;u++){var c=this.parameters[u];"undefined"!=typeof t[c.name]?"body"===c["in"]?i=t[c.name]:"formData"===c["in"]&&(s[c.name]={param:c,value:t[c.name]},r=!0):"body"===c["in"]&&(l=!0)}if(l&&"undefined"==typeof i){var p=e["Content-Type"];p&&0===p.indexOf("application/json")&&(i="{}")}var h=!1;if(e["Content-Type"]&&e["Content-Type"].indexOf("multipart/form-data")>=0&&(h=!0),r&&!h){var f="";for(a in s){var c=s[a].param;o=s[a].value,"undefined"!=typeof o&&(Array.isArray(o)?(""!==f&&(f+="&"),f+=this.encodeQueryCollection(c.collectionFormat,a,o)):(""!==f&&(f+="&"),f+=encodeURIComponent(a)+"="+encodeURIComponent(o)))}i=f}else if(h){if("function"==typeof FormData){var d=new FormData;d.type="formData";for(a in s)o=t[a],"undefined"!=typeof o&&("[object File]"==={}.toString.apply(o)?d.append(a,o):"file"===o.type&&o.value?d.append(a,o.value):Array.isArray(o)?d.append(a,this.encodeQueryCollection(c.collectionFormat,a,o)):d.append(a,o));
i=d}else{d={};for(a in s)if(o=t[a],Array.isArray(o)){var m,y=c.collectionFormat||"multi";m="ssv"===y?" ":"pipes"===y?"|":"tsv"===y?"	":",";var g;o.forEach(function(e){g?g+=m:g="",g+=e}),d[a]=g}else d[a]=o;i=d}e["Content-Type"]="multipart/form-data"}return i},u.prototype.getModelSampleJSON=function(e,t){var n,r,a;if(t=t||{},n=e instanceof Array,a=n?e[0]:e,t[a]?r=t[a].createJSONSample():this.getInlineModel(a)&&(r=this.getInlineModel(a).createJSONSample()),r){if(r=n?[r]:r,"string"==typeof r)return r;if(i.isObject(r)){var o=r;if(r instanceof Array&&r.length>0&&(o=r[0]),o.nodeName&&"Node"==typeof o){var s=(new XMLSerializer).serializeToString(o);return this.formatXml(s)}return JSON.stringify(r,null,2)}return r}},u.prototype["do"]=function(e,t,n,r,i){return this.execute(e,t,n,r,i)},u.prototype.execute=function(e,t,n,r,o){var u,c,p,h=e||{},f={};i.isObject(t)&&(f=t,u=n,c=r),this.client&&(f.client=this.client),!f.requestInterceptor&&this.requestInterceptor&&(f.requestInterceptor=this.requestInterceptor),!f.responseInterceptor&&this.responseInterceptor&&(f.responseInterceptor=this.responseInterceptor),"function"==typeof t&&(u=t,c=n),this.parent.usePromise?p=l.defer():(u=u||this.parent.defaultSuccessCallback||a.log,c=c||this.parent.defaultErrorCallback||a.log),"undefined"==typeof f.useJQuery&&(f.useJQuery=this.useJQuery),"undefined"==typeof f.jqueryAjaxCache&&(f.jqueryAjaxCache=this.jqueryAjaxCache),"undefined"==typeof f.enableCookies&&(f.enableCookies=this.enableCookies);var d=this.getMissingParams(h);if(d.length>0){var m="missing required params: "+d;return a.fail(m),this.parent.usePromise?(p.reject(m),p.promise):(c(m,o),{})}var y,g=this.getHeaderParams(h),v=this.setContentTypes(h,f),b={};for(y in g)b[y]=g[y];for(y in v)b[y]=v[y];var w=this.getBody(v,h,f),x=this.urlify(h);if(x.indexOf(".{format}")>0&&b){var j=b.Accept||b.accept;j&&j.indexOf("json")>0?x=x.replace(".{format}",".json"):j&&j.indexOf("xml")>0&&(x=x.replace(".{format}",".xml"))}var O={url:x,method:this.method.toUpperCase(),body:w,enableCookies:f.enableCookies,useJQuery:f.useJQuery,jqueryAjaxCache:f.jqueryAjaxCache,deferred:p,headers:b,clientAuthorizations:f.clientAuthorizations,on:{response:function(e){return p?(p.resolve(e),p.promise):u(e,o)},error:function(e){return p?(p.reject(e),p.promise):c(e,o)}}};return this.clientAuthorizations.apply(O,this.operation.security),f.mock===!0?O:(new s).execute(O,f)},u.prototype.setContentTypes=function(e,t){var n,i,o=this.parameters,s=e.parameterContentType||r(this.consumes,["application/json","application/yaml"]),l=t.responseContentType||r(this.produces,["application/json","application/yaml"]),u=[],c=[],p={};for(i=0;i<o.length;i++){var h=o[i];if("formData"===h["in"])"file"===h.type?u.push(h):c.push(h);else if("header"===h["in"]&&t){var f=h.name,d=t[h.name];"undefined"!=typeof t[h.name]&&(p[f]=d)}else"body"===h["in"]&&"undefined"!=typeof e[h.name]&&(n=e[h.name])}var m=n||u.length||c.length;if("post"===this.method||"put"===this.method||"patch"===this.method||("delete"===this.method||"get"===this.method)&&m){if(t.requestContentType&&(s=t.requestContentType),c.length>0){if(s=void 0,t.requestContentType)s=t.requestContentType;else if(u.length>0)s="multipart/form-data";else if(this.consumes&&this.consumes.length>0)for(var y in this.consumes){var g=this.consumes[y];0!==g.indexOf("application/x-www-form-urlencoded")&&0!==g.indexOf("multipart/form-data")||(s=g)}"undefined"==typeof s&&(s="application/x-www-form-urlencoded")}}else s=null;return s&&this.consumes&&-1===this.consumes.indexOf(s)&&a.log("server doesn't consume "+s+", try "+JSON.stringify(this.consumes)),this.matchesAccept(l)||a.log("server can't produce "+l),s&&""!==n||"application/x-www-form-urlencoded"===s?p["Content-Type"]=s:this.consumes&&this.consumes.length>0&&"application/x-www-form-urlencoded"===this.consumes[0]&&(p["Content-Type"]=this.consumes[0]),l&&(p.Accept=l),p},u.prototype.matchesAccept=function(e){return e&&this.produces?-1!==this.produces.indexOf(e)||-1!==this.produces.indexOf("*/*"):!0},u.prototype.asCurl=function(e,t){var n={mock:!0};if("object"==typeof t)for(var r in t)n[r]=t[r];var a=this.execute(e,n);this.clientAuthorizations.apply(a,this.operation.security);var o=[];if(o.push("-X "+this.method.toUpperCase()),"undefined"!=typeof a.headers){var s;for(s in a.headers){var l=a.headers[s];"string"==typeof l&&(l=l.replace(/\'/g,"\\u0027")),o.push("--header '"+s+": "+l+"'")}}var u=!1,c=!1,p=a.headers["Content-Type"];if(p&&0===p.indexOf("application/x-www-form-urlencoded")?u=!0:p&&0===p.indexOf("multipart/form-data")&&(u=!0,c=!0),a.body){var h;if(i.isObject(a.body)){if(c){c=!0;for(var f=0;f<this.parameters.length;f++){var d=this.parameters[f];if("formData"===d["in"]){h||(h="");var m;m="function"==typeof FormData&&a.body instanceof FormData?a.body.get(d.name):a.body[d.name],m&&("file"===d.type?m.name&&(h+="-F "+d.name+'=@"'+m.name+'" '):(h+="-F ",h+=Array.isArray(m)?this.encodeQueryCollection(d.collectionFormat,d.name,m):this.encodeQueryKey(d.name)+"="+m,h+=" "))}}}h||(h=JSON.stringify(a.body))}else h=a.body;h=h.replace(/\'/g,"%27").replace(/\n/g," \\ \n "),u||(h=h.replace(/&/g,"%26")),c?o.push(h):o.push("-d '"+h.replace(/@/g,"%40")+"'")}return"curl "+o.join(" ")+" '"+a.url+"'"},u.prototype.encodePathCollection=function(e,t,n){var r,i="",a="";for(a="ssv"===e?"%20":"tsv"===e?"%09":"pipes"===e?"|":",",r=0;r<n.length;r++)0===r?i=this.encodeQueryParam(n[r]):i+=a+this.encodeQueryParam(n[r]);return i},u.prototype.encodeQueryCollection=function(e,t,n){var r,i="";if(e=e||"default","default"===e||"multi"===e)for(r=0;r<n.length;r++)r>0&&(i+="&"),i+=this.encodeQueryKey(t)+"="+this.encodeQueryParam(n[r]);else{var a="";if("csv"===e)a=",";else if("ssv"===e)a="%20";else if("tsv"===e)a="%09";else if("pipes"===e)a="|";else if("brackets"===e)for(r=0;r<n.length;r++)0!==r&&(i+="&"),i+=this.encodeQueryKey(t)+"[]="+this.encodeQueryParam(n[r]);if(""!==a)for(r=0;r<n.length;r++)0===r?i=this.encodeQueryKey(t)+"="+this.encodeQueryParam(n[r]):i+=a+this.encodeQueryParam(n[r])}return i},u.prototype.encodeQueryKey=function(e){return encodeURIComponent(e).replace("%5B","[").replace("%5D","]").replace("%24","$")},u.prototype.encodeQueryParam=function(e){return encodeURIComponent(e)},u.prototype.encodePathParam=function(e){return encodeURIComponent(e)}},{"../helpers":4,"../http":5,"./model":9,"lodash-compat/lang/cloneDeep":138,"lodash-compat/lang/isEmpty":141,"lodash-compat/lang/isObject":144,"lodash-compat/lang/isUndefined":148,q:157}],11:[function(e,t,n){"use strict";var r=t.exports=function(e,t,n,r){this.description=t,this.externalDocs=n,this.name=e,this.operation=r,this.operationsArray=[],this.path=e,this.tag=e};r.prototype.sort=function(){}},{}],12:[function(e,t,n){function r(){if(!s){s=!0;for(var e,t=o.length;t;){e=o,o=[];for(var n=-1;++n<t;)e[n]();t=o.length}s=!1}}function i(){}var a=t.exports={},o=[],s=!1;a.nextTick=function(e){o.push(e),s||setTimeout(r,0)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=i,a.addListener=i,a.once=i,a.off=i,a.removeListener=i,a.removeAllListeners=i,a.emit=i,a.binding=function(e){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},{}],13:[function(e,t,n){(function(e){!function(){"use strict";function n(t){var n;return n=t instanceof e?t:new e(t.toString(),"binary"),n.toString("base64")}t.exports=n}()}).call(this,e("buffer").Buffer)},{buffer:14}],14:[function(e,t,n){function r(){return i.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function i(e){return this instanceof i?(this.length=0,this.parent=void 0,"number"==typeof e?a(this,e):"string"==typeof e?o(this,e,arguments.length>1?arguments[1]:"utf8"):s(this,e)):arguments.length>1?new i(e,arguments[1]):new i(e)}function a(e,t){if(e=d(e,0>t?0:0|m(t)),!i.TYPED_ARRAY_SUPPORT)for(var n=0;t>n;n++)e[n]=0;return e}function o(e,t,n){"string"==typeof n&&""!==n||(n="utf8");var r=0|g(t,n);return e=d(e,r),e.write(t,n),e}function s(e,t){if(i.isBuffer(t))return l(e,t);if(K(t))return u(e,t);if(null==t)throw new TypeError("must start with number, buffer, array or string");if("undefined"!=typeof ArrayBuffer){if(t.buffer instanceof ArrayBuffer)return c(e,t);if(t instanceof ArrayBuffer)return p(e,t)}return t.length?h(e,t):f(e,t)}function l(e,t){var n=0|m(t.length);return e=d(e,n),t.copy(e,0,0,n),e}function u(e,t){var n=0|m(t.length);e=d(e,n);for(var r=0;n>r;r+=1)e[r]=255&t[r];return e}function c(e,t){var n=0|m(t.length);e=d(e,n);for(var r=0;n>r;r+=1)e[r]=255&t[r];return e}function p(e,t){return i.TYPED_ARRAY_SUPPORT?(t.byteLength,e=i._augment(new Uint8Array(t))):e=c(e,new Uint8Array(t)),e}function h(e,t){var n=0|m(t.length);e=d(e,n);for(var r=0;n>r;r+=1)e[r]=255&t[r];return e}function f(e,t){var n,r=0;"Buffer"===t.type&&K(t.data)&&(n=t.data,r=0|m(n.length)),e=d(e,r);for(var i=0;r>i;i+=1)e[i]=255&n[i];return e}function d(e,t){i.TYPED_ARRAY_SUPPORT?e=i._augment(new Uint8Array(t)):(e.length=t,e._isBuffer=!0);var n=0!==t&&t<=i.poolSize>>>1;return n&&(e.parent=W),e}function m(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function y(e,t){if(!(this instanceof y))return new y(e,t);var n=new i(e,t);return delete n.parent,n}function g(e,t){"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"binary":case"raw":case"raws":return n;case"utf8":case"utf-8":return q(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return H(e).length;default:if(r)return q(e).length;t=(""+t).toLowerCase(),r=!0}}function v(e,t,n){var r=!1;if(t=0|t,n=void 0===n||n===1/0?this.length:0|n,e||(e="utf8"),0>t&&(t=0),n>this.length&&(n=this.length),t>=n)return"";for(;;)switch(e){case"hex":return I(this,t,n);case"utf8":case"utf-8":return _(this,t,n);case"ascii":return E(this,t,n);case"binary":return C(this,t,n);case"base64":return S(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function b(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var a=t.length;if(a%2!==0)throw new Error("Invalid hex string");r>a/2&&(r=a/2);for(var o=0;r>o;o++){var s=parseInt(t.substr(2*o,2),16);if(isNaN(s))throw new Error("Invalid hex string");e[n+o]=s}return o}function w(e,t,n,r){return J(q(t,e.length-n),e,n,r)}function x(e,t,n,r){return J(V(t),e,n,r)}function j(e,t,n,r){return x(e,t,n,r)}function O(e,t,n,r){return J(H(t),e,n,r)}function A(e,t,n,r){return J(z(t,e.length-n),e,n,r)}function S(e,t,n){return 0===t&&n===e.length?Y.fromByteArray(e):Y.fromByteArray(e.slice(t,n))}function _(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;n>i;){var a=e[i],o=null,s=a>239?4:a>223?3:a>191?2:1;if(n>=i+s){var l,u,c,p;switch(s){case 1:128>a&&(o=a);break;case 2:l=e[i+1],128===(192&l)&&(p=(31&a)<<6|63&l,p>127&&(o=p));break;case 3:l=e[i+1],u=e[i+2],128===(192&l)&&128===(192&u)&&(p=(15&a)<<12|(63&l)<<6|63&u,p>2047&&(55296>p||p>57343)&&(o=p));break;case 4:l=e[i+1],u=e[i+2],c=e[i+3],128===(192&l)&&128===(192&u)&&128===(192&c)&&(p=(15&a)<<18|(63&l)<<12|(63&u)<<6|63&c,p>65535&&1114112>p&&(o=p))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}return k(r)}function k(e){var t=e.length;if(X>=t)return String.fromCharCode.apply(String,e);for(var n="",r=0;t>r;)n+=String.fromCharCode.apply(String,e.slice(r,r+=X));return n}function E(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;n>i;i++)r+=String.fromCharCode(127&e[i]);return r}function C(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;n>i;i++)r+=String.fromCharCode(e[i]);return r}function I(e,t,n){var r=e.length;(!t||0>t)&&(t=0),(!n||0>n||n>r)&&(n=r);for(var i="",a=t;n>a;a++)i+=B(e[a]);return i}function T(e,t,n){for(var r=e.slice(t,n),i="",a=0;a<r.length;a+=2)i+=String.fromCharCode(r[a]+256*r[a+1]);return i}function M(e,t,n){if(e%1!==0||0>e)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function $(e,t,n,r,a,o){if(!i.isBuffer(e))throw new TypeError("buffer must be a Buffer instance");if(t>a||o>t)throw new RangeError("value is out of bounds");if(n+r>e.length)throw new RangeError("index out of range")}function U(e,t,n,r){0>t&&(t=65535+t+1);for(var i=0,a=Math.min(e.length-n,2);a>i;i++)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function D(e,t,n,r){0>t&&(t=4294967295+t+1);for(var i=0,a=Math.min(e.length-n,4);a>i;i++)e[n+i]=t>>>8*(r?i:3-i)&255}function P(e,t,n,r,i,a){if(t>i||a>t)throw new RangeError("value is out of bounds");if(n+r>e.length)throw new RangeError("index out of range");if(0>n)throw new RangeError("index out of range")}function L(e,t,n,r,i){return i||P(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),Q.write(e,t,n,r,23,4),n+4}function R(e,t,n,r,i){return i||P(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),Q.write(e,t,n,r,52,8),n+8}function N(e){if(e=F(e).replace(Z,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function F(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function B(e){return 16>e?"0"+e.toString(16):e.toString(16)}function q(e,t){t=t||1/0;for(var n,r=e.length,i=null,a=[],o=0;r>o;o++){if(n=e.charCodeAt(o),n>55295&&57344>n){if(!i){if(n>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(o+1===r){(t-=3)>-1&&a.push(239,191,189);continue}i=n;continue}if(56320>n){(t-=3)>-1&&a.push(239,191,189),i=n;continue}n=i-55296<<10|n-56320|65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,128>n){if((t-=1)<0)break;a.push(n)}else if(2048>n){if((t-=2)<0)break;a.push(n>>6|192,63&n|128)}else if(65536>n){if((t-=3)<0)break;a.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(1114112>n))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return a}function V(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t}function z(e,t){for(var n,r,i,a=[],o=0;o<e.length&&!((t-=2)<0);o++)n=e.charCodeAt(o),r=n>>8,i=n%256,a.push(i),a.push(r);return a}function H(e){return Y.toByteArray(N(e))}function J(e,t,n,r){for(var i=0;r>i&&!(i+n>=t.length||i>=e.length);i++)t[i+n]=e[i];return i}var Y=e("base64-js"),Q=e("ieee754"),K=e("is-array");n.Buffer=i,n.SlowBuffer=y,n.INSPECT_MAX_BYTES=50,i.poolSize=8192;var W={};i.TYPED_ARRAY_SUPPORT=function(){function e(){}try{var t=new Uint8Array(1);return t.foo=function(){return 42},t.constructor=e,42===t.foo()&&t.constructor===e&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(n){return!1}}(),i.isBuffer=function(e){return!(null==e||!e._isBuffer)},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,a=0,o=Math.min(n,r);o>a&&e[a]===t[a];)++a;return a!==o&&(n=e[a],r=t[a]),r>n?-1:n>r?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!K(e))throw new TypeError("list argument must be an Array of Buffers.");if(0===e.length)return new i(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;n++)t+=e[n].length;var r=new i(t),a=0;for(n=0;n<e.length;n++){var o=e[n];o.copy(r,a),a+=o.length}return r},i.byteLength=g,i.prototype.length=void 0,i.prototype.parent=void 0,i.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?_(this,0,e):v.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:0===i.compare(this,e)},i.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?0:i.compare(this,e)},i.prototype.indexOf=function(e,t){function n(e,t,n){for(var r=-1,i=0;n+i<e.length;i++)if(e[n+i]===t[-1===r?0:i-r]){if(-1===r&&(r=i),i-r+1===t.length)return n+r}else r=-1;return-1}if(t>2147483647?t=2147483647:-2147483648>t&&(t=-2147483648),t>>=0,0===this.length)return-1;if(t>=this.length)return-1;if(0>t&&(t=Math.max(this.length+t,0)),"string"==typeof e)return 0===e.length?-1:String.prototype.indexOf.call(this,e,t);if(i.isBuffer(e))return n(this,e,t);if("number"==typeof e)return i.TYPED_ARRAY_SUPPORT&&"function"===Uint8Array.prototype.indexOf?Uint8Array.prototype.indexOf.call(this,e,t):n(this,[e],t);throw new TypeError("val must be string, number or Buffer")},i.prototype.get=function(e){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(e)},i.prototype.set=function(e,t){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(e,t)},i.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else if(isFinite(t))t=0|t,isFinite(n)?(n=0|n,void 0===r&&(r="utf8")):(r=n,n=void 0);else{var i=r;r=t,t=0|n,n=i}var a=this.length-t;if((void 0===n||n>a)&&(n=a),e.length>0&&(0>n||0>t)||t>this.length)throw new RangeError("attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return b(this,e,t,n);case"utf8":case"utf-8":return w(this,e,t,n);case"ascii":return x(this,e,t,n);case"binary":return j(this,e,t,n);case"base64":return O(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var X=4096;i.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,0>e?(e+=n,0>e&&(e=0)):e>n&&(e=n),0>t?(t+=n,0>t&&(t=0)):t>n&&(t=n),e>t&&(t=e);var r;if(i.TYPED_ARRAY_SUPPORT)r=i._augment(this.subarray(e,t));else{var a=t-e;r=new i(a,void 0);for(var o=0;a>o;o++)r[o]=this[o+e]}return r.length&&(r.parent=this.parent||this),r},i.prototype.readUIntLE=function(e,t,n){e=0|e,t=0|t,n||M(e,t,this.length);for(var r=this[e],i=1,a=0;++a<t&&(i*=256);)r+=this[e+a]*i;return r},i.prototype.readUIntBE=function(e,t,n){e=0|e,t=0|t,n||M(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},i.prototype.readUInt8=function(e,t){return t||M(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return t||M(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return t||M(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return t||M(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return t||M(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,n){e=0|e,t=0|t,n||M(e,t,this.length);for(var r=this[e],i=1,a=0;++a<t&&(i*=256);)r+=this[e+a]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},i.prototype.readIntBE=function(e,t,n){e=0|e,t=0|t,n||M(e,t,this.length);for(var r=t,i=1,a=this[e+--r];r>0&&(i*=256);)a+=this[e+--r]*i;return i*=128,a>=i&&(a-=Math.pow(2,8*t)),a},i.prototype.readInt8=function(e,t){return t||M(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){t||M(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(e,t){t||M(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(e,t){return t||M(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return t||M(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return t||M(e,4,this.length),Q.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return t||M(e,4,this.length),Q.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return t||M(e,8,this.length),Q.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return t||M(e,8,this.length),Q.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,n,r){e=+e,t=0|t,n=0|n,r||$(this,e,t,n,Math.pow(2,8*n),0);var i=1,a=0;for(this[t]=255&e;++a<n&&(i*=256);)this[t+a]=e/i&255;return t+n},i.prototype.writeUIntBE=function(e,t,n,r){e=+e,t=0|t,n=0|n,r||$(this,e,t,n,Math.pow(2,8*n),0);var i=n-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+n},i.prototype.writeUInt8=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,1,255,0),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=e,t+1},i.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e,this[t+1]=e>>>8):U(this,e,t,!0),t+2},i.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=e):U(this,e,t,!1),t+2},i.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e):D(this,e,t,!0),t+4},i.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e):D(this,e,t,!1),t+4},i.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t=0|t,!r){var i=Math.pow(2,8*n-1);$(this,e,t,n,i-1,-i)}var a=0,o=1,s=0>e?1:0;for(this[t]=255&e;++a<n&&(o*=256);)this[t+a]=(e/o>>0)-s&255;return t+n},i.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t=0|t,!r){var i=Math.pow(2,8*n-1);$(this,e,t,n,i-1,-i)}var a=n-1,o=1,s=0>e?1:0;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=(e/o>>0)-s&255;return t+n},i.prototype.writeInt8=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,1,127,-128),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),0>e&&(e=255+e+1),this[t]=e,t+1},i.prototype.writeInt16LE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=e,this[t+1]=e>>>8):U(this,e,t,!0),t+2},i.prototype.writeInt16BE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=e):U(this,e,t,!1),t+2},i.prototype.writeInt32LE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,4,2147483647,-2147483648),i.TYPED_ARRAY_SUPPORT?(this[t]=e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):D(this,e,t,!0),t+4},i.prototype.writeInt32BE=function(e,t,n){return e=+e,t=0|t,n||$(this,e,t,4,2147483647,-2147483648),0>e&&(e=4294967295+e+1),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e):D(this,e,t,!1),t+4},i.prototype.writeFloatLE=function(e,t,n){return L(this,e,t,!0,n)},i.prototype.writeFloatBE=function(e,t,n){return L(this,e,t,!1,n)},i.prototype.writeDoubleLE=function(e,t,n){return R(this,e,t,!0,n)},i.prototype.writeDoubleBE=function(e,t,n){return R(this,e,t,!1,n)},i.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&n>r&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(0>t)throw new RangeError("targetStart out of bounds");if(0>n||n>=this.length)throw new RangeError("sourceStart out of bounds");if(0>r)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var a,o=r-n;if(this===e&&t>n&&r>t)for(a=o-1;a>=0;a--)e[a+t]=this[a+n];else if(1e3>o||!i.TYPED_ARRAY_SUPPORT)for(a=0;o>a;a++)e[a+t]=this[a+n];else e._set(this.subarray(n,n+o),t);return o},i.prototype.fill=function(e,t,n){if(e||(e=0),t||(t=0),n||(n=this.length),t>n)throw new RangeError("end < start");if(n!==t&&0!==this.length){if(0>t||t>=this.length)throw new RangeError("start out of bounds");if(0>n||n>this.length)throw new RangeError("end out of bounds");var r;if("number"==typeof e)for(r=t;n>r;r++)this[r]=e;else{var i=q(e.toString()),a=i.length;for(r=t;n>r;r++)this[r]=i[r%a]}return this}},i.prototype.toArrayBuffer=function(){if("undefined"!=typeof Uint8Array){if(i.TYPED_ARRAY_SUPPORT)return new i(this).buffer;for(var e=new Uint8Array(this.length),t=0,n=e.length;n>t;t+=1)e[t]=this[t];return e.buffer}throw new TypeError("Buffer.toArrayBuffer not supported in this browser")};var G=i.prototype;i._augment=function(e){return e.constructor=i,e._isBuffer=!0,e._set=e.set,e.get=G.get,e.set=G.set,e.write=G.write,e.toString=G.toString,e.toLocaleString=G.toString,e.toJSON=G.toJSON,e.equals=G.equals,e.compare=G.compare,e.indexOf=G.indexOf,e.copy=G.copy,e.slice=G.slice,e.readUIntLE=G.readUIntLE,e.readUIntBE=G.readUIntBE,e.readUInt8=G.readUInt8,e.readUInt16LE=G.readUInt16LE,e.readUInt16BE=G.readUInt16BE,e.readUInt32LE=G.readUInt32LE,e.readUInt32BE=G.readUInt32BE,e.readIntLE=G.readIntLE,e.readIntBE=G.readIntBE,e.readInt8=G.readInt8,e.readInt16LE=G.readInt16LE,e.readInt16BE=G.readInt16BE,e.readInt32LE=G.readInt32LE,e.readInt32BE=G.readInt32BE,e.readFloatLE=G.readFloatLE,e.readFloatBE=G.readFloatBE,e.readDoubleLE=G.readDoubleLE,e.readDoubleBE=G.readDoubleBE,e.writeUInt8=G.writeUInt8,e.writeUIntLE=G.writeUIntLE,e.writeUIntBE=G.writeUIntBE,e.writeUInt16LE=G.writeUInt16LE,e.writeUInt16BE=G.writeUInt16BE,e.writeUInt32LE=G.writeUInt32LE,e.writeUInt32BE=G.writeUInt32BE,e.writeIntLE=G.writeIntLE,e.writeIntBE=G.writeIntBE,e.writeInt8=G.writeInt8,e.writeInt16LE=G.writeInt16LE,e.writeInt16BE=G.writeInt16BE,e.writeInt32LE=G.writeInt32LE,e.writeInt32BE=G.writeInt32BE,e.writeFloatLE=G.writeFloatLE,e.writeFloatBE=G.writeFloatBE,e.writeDoubleLE=G.writeDoubleLE,e.writeDoubleBE=G.writeDoubleBE,e.fill=G.fill,e.inspect=G.inspect,e.toArrayBuffer=G.toArrayBuffer,e};var Z=/[^+\/0-9A-Za-z-_]/g},{"base64-js":15,ieee754:16,"is-array":17}],15:[function(e,t,n){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";!function(e){"use strict";function t(e){var t=e.charCodeAt(0);return t===o||t===p?62:t===s||t===h?63:l>t?-1:l+10>t?t-l+26+26:c+26>t?t-c:u+26>t?t-u+26:void 0}function n(e){function n(e){u[p++]=e}var r,i,o,s,l,u;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var c=e.length;l="="===e.charAt(c-2)?2:"="===e.charAt(c-1)?1:0,u=new a(3*e.length/4-l),o=l>0?e.length-4:e.length;var p=0;for(r=0,i=0;o>r;r+=4,i+=3)s=t(e.charAt(r))<<18|t(e.charAt(r+1))<<12|t(e.charAt(r+2))<<6|t(e.charAt(r+3)),n((16711680&s)>>16),n((65280&s)>>8),n(255&s);return 2===l?(s=t(e.charAt(r))<<2|t(e.charAt(r+1))>>4,n(255&s)):1===l&&(s=t(e.charAt(r))<<10|t(e.charAt(r+1))<<4|t(e.charAt(r+2))>>2,n(s>>8&255),n(255&s)),u}function i(e){function t(e){return r.charAt(e)}function n(e){return t(e>>18&63)+t(e>>12&63)+t(e>>6&63)+t(63&e)}var i,a,o,s=e.length%3,l="";for(i=0,o=e.length-s;o>i;i+=3)a=(e[i]<<16)+(e[i+1]<<8)+e[i+2],l+=n(a);switch(s){case 1:a=e[e.length-1],l+=t(a>>2),l+=t(a<<4&63),l+="==";break;case 2:a=(e[e.length-2]<<8)+e[e.length-1],l+=t(a>>10),l+=t(a>>4&63),l+=t(a<<2&63),l+="="}return l}var a="undefined"!=typeof Uint8Array?Uint8Array:Array,o="+".charCodeAt(0),s="/".charCodeAt(0),l="0".charCodeAt(0),u="a".charCodeAt(0),c="A".charCodeAt(0),p="-".charCodeAt(0),h="_".charCodeAt(0);e.toByteArray=n,e.fromByteArray=i}("undefined"==typeof n?this.base64js={}:n)},{}],16:[function(e,t,n){n.read=function(e,t,n,r,i){var a,o,s=8*i-r-1,l=(1<<s)-1,u=l>>1,c=-7,p=n?i-1:0,h=n?-1:1,f=e[t+p];for(p+=h,a=f&(1<<-c)-1,f>>=-c,c+=s;c>0;a=256*a+e[t+p],p+=h,c-=8);for(o=a&(1<<-c)-1,a>>=-c,c+=r;c>0;o=256*o+e[t+p],p+=h,c-=8);if(0===a)a=1-u;else{if(a===l)return o?NaN:(f?-1:1)*(1/0);o+=Math.pow(2,r),a-=u}return(f?-1:1)*o*Math.pow(2,a-r)},n.write=function(e,t,n,r,i,a){var o,s,l,u=8*a-i-1,c=(1<<u)-1,p=c>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:a-1,d=r?1:-1,m=0>t||0===t&&0>1/t?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),t+=o+p>=1?h/l:h*Math.pow(2,1-p),t*l>=2&&(o++,l/=2),o+p>=c?(s=0,o=c):o+p>=1?(s=(t*l-1)*Math.pow(2,i),o+=p):(s=t*Math.pow(2,p-1)*Math.pow(2,i),o=0));i>=8;e[n+f]=255&s,f+=d,s/=256,i-=8);for(o=o<<i|s,u+=i;u>0;e[n+f]=255&o,f+=d,o/=256,u-=8);e[n+f-d]|=128*m}},{}],17:[function(e,t,n){var r=Array.isArray,i=Object.prototype.toString;t.exports=r||function(e){return!!e&&"[object Array]"==i.call(e)}},{}],18:[function(e,t,n){!function(){"use strict";function e(t,n,r,i){return this instanceof e?(this.domain=t||void 0,this.path=n||"/",this.secure=!!r,this.script=!!i,this):new e(t,n,r,i)}function t(e,n,r){return e instanceof t?e:this instanceof t?(this.name=null,this.value=null,this.expiration_date=1/0,this.path=String(r||"/"),this.explicit_path=!1,this.domain=n||null,this.explicit_domain=!1,this.secure=!1,this.noscript=!1,e&&this.parse(e,n,r),this):new t(e,n,r)}function r(){var e,n,i;return this instanceof r?(e=Object.create(null),this.setCookie=function(r,a,o){var s,l;if(r=new t(r,a,o),s=r.expiration_date<=Date.now(),void 0!==e[r.name]){for(n=e[r.name],l=0;l<n.length;l+=1)if(i=n[l],i.collidesWith(r))return s?(n.splice(l,1),0===n.length&&delete e[r.name],!1):(n[l]=r,r);return s?!1:(n.push(r),r)}return s?!1:(e[r.name]=[r],e[r.name])},this.getCookie=function(t,r){var i,a;if(n=e[t])for(a=0;a<n.length;a+=1)if(i=n[a],i.expiration_date<=Date.now())0===n.length&&delete e[i.name];else if(i.matches(r))return i},this.getCookies=function(t){var n,r,i=[];for(n in e)r=this.getCookie(n,t),r&&i.push(r);return i.toString=function(){return i.join(":")},i.toValueString=function(){return i.map(function(e){return e.toValueString()}).join(";")},i},this):new r}n.CookieAccessInfo=e,n.Cookie=t,t.prototype.toString=function(){var e=[this.name+"="+this.value];return this.expiration_date!==1/0&&e.push("expires="+new Date(this.expiration_date).toGMTString()),this.domain&&e.push("domain="+this.domain),this.path&&e.push("path="+this.path),this.secure&&e.push("secure"),this.noscript&&e.push("httponly"),e.join("; ")},t.prototype.toValueString=function(){return this.name+"="+this.value};var i=/[:](?=\s*[a-zA-Z0-9_\-]+\s*[=])/g;t.prototype.parse=function(e,n,r){if(this instanceof t){var i,a=e.split(";").filter(function(e){return!!e}),o=a[0].match(/([^=]+)=([\s\S]*)/),s=o[1],l=o[2];for(this.name=s,this.value=l,i=1;i<a.length;i+=1)switch(o=a[i].match(/([^=]+)(?:=([\s\S]*))?/),s=o[1].trim().toLowerCase(),l=o[2],s){case"httponly":this.noscript=!0;break;case"expires":this.expiration_date=l?Number(Date.parse(l)):1/0;break;case"path":this.path=l?l.trim():"",this.explicit_path=!0;break;case"domain":this.domain=l?l.trim():"",this.explicit_domain=!!this.domain;break;case"secure":this.secure=!0}return this.explicit_path||(this.path=r||"/"),this.explicit_domain||(this.domain=n),this}return(new t).parse(e,n,r)},t.prototype.matches=function(e){return!(this.noscript&&e.script||this.secure&&!e.secure)&&this.collidesWith(e)},t.prototype.collidesWith=function(e){if(this.path&&!e.path||this.domain&&!e.domain)return!1;if(this.path&&0!==e.path.indexOf(this.path))return!1;if(this.explicit_path&&0!==e.path.indexOf(this.path))return!1;var t=e.domain&&e.domain.replace(/^[\.]/,""),n=this.domain&&this.domain.replace(/^[\.]/,"");if(n===t)return!0;if(n){if(!this.explicit_domain)return!1;var r=t.indexOf(n);return-1!==r&&r===t.length-n.length}return!0},n.CookieJar=r,r.prototype.setCookies=function(e,n,r){e=Array.isArray(e)?e:e.split(i);var a,o,s=[];for(e=e.map(function(e){return new t(e,n,r)}),a=0;a<e.length;a+=1)o=e[a],this.setCookie(o,n,r)&&s.push(o);return s}}()},{}],19:[function(e,t,n){"use strict";var r=e("./lib/js-yaml.js");t.exports=r},{"./lib/js-yaml.js":20}],20:[function(e,t,n){"use strict";function r(e){return function(){throw new Error("Function "+e+" is deprecated and cannot be used.")}}var i=e("./js-yaml/loader"),a=e("./js-yaml/dumper");t.exports.Type=e("./js-yaml/type"),t.exports.Schema=e("./js-yaml/schema"),t.exports.FAILSAFE_SCHEMA=e("./js-yaml/schema/failsafe"),t.exports.JSON_SCHEMA=e("./js-yaml/schema/json"),t.exports.CORE_SCHEMA=e("./js-yaml/schema/core"),
t.exports.DEFAULT_SAFE_SCHEMA=e("./js-yaml/schema/default_safe"),t.exports.DEFAULT_FULL_SCHEMA=e("./js-yaml/schema/default_full"),t.exports.load=i.load,t.exports.loadAll=i.loadAll,t.exports.safeLoad=i.safeLoad,t.exports.safeLoadAll=i.safeLoadAll,t.exports.dump=a.dump,t.exports.safeDump=a.safeDump,t.exports.YAMLException=e("./js-yaml/exception"),t.exports.MINIMAL_SCHEMA=e("./js-yaml/schema/failsafe"),t.exports.SAFE_SCHEMA=e("./js-yaml/schema/default_safe"),t.exports.DEFAULT_SCHEMA=e("./js-yaml/schema/default_full"),t.exports.scan=r("scan"),t.exports.parse=r("parse"),t.exports.compose=r("compose"),t.exports.addConstructor=r("addConstructor")},{"./js-yaml/dumper":22,"./js-yaml/exception":23,"./js-yaml/loader":24,"./js-yaml/schema":26,"./js-yaml/schema/core":27,"./js-yaml/schema/default_full":28,"./js-yaml/schema/default_safe":29,"./js-yaml/schema/failsafe":30,"./js-yaml/schema/json":31,"./js-yaml/type":32}],21:[function(e,t,n){"use strict";function r(e){return"undefined"==typeof e||null===e}function i(e){return"object"==typeof e&&null!==e}function a(e){return Array.isArray(e)?e:r(e)?[]:[e]}function o(e,t){var n,r,i,a;if(t)for(a=Object.keys(t),n=0,r=a.length;r>n;n+=1)i=a[n],e[i]=t[i];return e}function s(e,t){var n,r="";for(n=0;t>n;n+=1)r+=e;return r}function l(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e}t.exports.isNothing=r,t.exports.isObject=i,t.exports.toArray=a,t.exports.repeat=s,t.exports.isNegativeZero=l,t.exports.extend=o},{}],22:[function(e,t,n){"use strict";function r(e,t){var n,r,i,a,o,s,l;if(null===t)return{};for(n={},r=Object.keys(t),i=0,a=r.length;a>i;i+=1)o=r[i],s=String(t[o]),"!!"===o.slice(0,2)&&(o="tag:yaml.org,2002:"+o.slice(2)),l=e.compiledTypeMap[o],l&&D.call(l.styleAliases,s)&&(s=l.styleAliases[s]),n[o]=s;return n}function i(e){var t,n,r;if(t=e.toString(16).toUpperCase(),255>=e)n="x",r=2;else if(65535>=e)n="u",r=4;else{if(!(4294967295>=e))throw new T("code point within a string may not be greater than 0xFFFFFFFF");n="U",r=8}return"\\"+n+I.repeat("0",r-t.length)+t}function a(e){this.schema=e.schema||M,this.indent=Math.max(1,e.indent||2),this.skipInvalid=e.skipInvalid||!1,this.flowLevel=I.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=r(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function o(e,t){for(var n,r=I.repeat(" ",t),i=0,a=-1,o="",s=e.length;s>i;)a=e.indexOf("\n",i),-1===a?(n=e.slice(i),i=s):(n=e.slice(i,a+1),i=a+1),n.length&&"\n"!==n&&(o+=r),o+=n;return o}function s(e,t){return"\n"+I.repeat(" ",e.indent*t)}function l(e,t){var n,r,i;for(n=0,r=e.implicitTypes.length;r>n;n+=1)if(i=e.implicitTypes[n],i.resolve(t))return!0;return!1}function u(e){return e===R||e===P}function c(e){return e>=32&&126>=e||e>=161&&55295>=e&&8232!==e&&8233!==e||e>=57344&&65533>=e&&65279!==e||e>=65536&&1114111>=e}function p(e){return c(e)&&65279!==e&&e!==J&&e!==G&&e!==Z&&e!==te&&e!==re&&e!==Q&&e!==B}function h(e){return c(e)&&65279!==e&&!u(e)&&e!==Y&&e!==W&&e!==Q&&e!==J&&e!==G&&e!==Z&&e!==te&&e!==re&&e!==B&&e!==V&&e!==H&&e!==N&&e!==ne&&e!==K&&e!==z&&e!==F&&e!==q&&e!==X&&e!==ee}function f(e,t,n,r,i){var a,o,s=!1,l=!1,f=-1!==r,d=-1,m=h(e.charCodeAt(0))&&!u(e.charCodeAt(e.length-1));if(t)for(a=0;a<e.length;a++){if(o=e.charCodeAt(a),!c(o))return ce;m=m&&p(o)}else{for(a=0;a<e.length;a++){if(o=e.charCodeAt(a),o===L)s=!0,f&&(l=l||a-d-1>r&&" "!==e[d+1],d=a);else if(!c(o))return ce;m=m&&p(o)}l=l||f&&a-d-1>r&&" "!==e[d+1]}return s||l?" "===e[0]&&n>9?ce:l?ue:le:m&&!i(e)?oe:se}function d(e,t,n,r){e.dump=function(){function i(t){return l(e,t)}if(0===t.length)return"''";if(!e.noCompatMode&&-1!==ae.indexOf(t))return"'"+t+"'";var a=e.indent*Math.max(1,n),s=-1===e.lineWidth?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-a),u=r||e.flowLevel>-1&&n>=e.flowLevel;switch(f(t,u,e.indent,s,i)){case oe:return t;case se:return"'"+t.replace(/'/g,"''")+"'";case le:return"|"+m(t,e.indent)+y(o(t,a));case ue:return">"+m(t,e.indent)+y(o(g(t,s),a));case ce:return'"'+b(t,s)+'"';default:throw new T("impossible error: invalid scalar style")}}()}function m(e,t){var n=" "===e[0]?String(t):"",r="\n"===e[e.length-1],i=r&&("\n"===e[e.length-2]||"\n"===e),a=i?"+":r?"":"-";return n+a+"\n"}function y(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function g(e,t){for(var n,r,i=/(\n+)([^\n]*)/g,a=function(){var n=e.indexOf("\n");return n=-1!==n?n:e.length,i.lastIndex=n,v(e.slice(0,n),t)}(),o="\n"===e[0]||" "===e[0];r=i.exec(e);){var s=r[1],l=r[2];n=" "===l[0],a+=s+(o||n||""===l?"":"\n")+v(l,t),o=n}return a}function v(e,t){if(""===e||" "===e[0])return e;for(var n,r,i=/ [^ ]/g,a=0,o=0,s=0,l="";n=i.exec(e);)s=n.index,s-a>t&&(r=o>a?o:s,l+="\n"+e.slice(a,r),a=r+1),o=s;return l+="\n",l+=e.length-a>t&&o>a?e.slice(a,o)+"\n"+e.slice(o+1):e.slice(a),l.slice(1)}function b(e){for(var t,n,r="",a=0;a<e.length;a++)t=e.charCodeAt(a),n=ie[t],r+=!n&&c(t)?e[a]:n||i(t);return r}function w(e,t,n){var r,i,a="",o=e.tag;for(r=0,i=n.length;i>r;r+=1)S(e,t,n[r],!1,!1)&&(0!==r&&(a+=", "),a+=e.dump);e.tag=o,e.dump="["+a+"]"}function x(e,t,n,r){var i,a,o="",l=e.tag;for(i=0,a=n.length;a>i;i+=1)S(e,t+1,n[i],!0,!0)&&(r&&0===i||(o+=s(e,t)),o+="- "+e.dump);e.tag=l,e.dump=o||"[]"}function j(e,t,n){var r,i,a,o,s,l="",u=e.tag,c=Object.keys(n);for(r=0,i=c.length;i>r;r+=1)s="",0!==r&&(s+=", "),a=c[r],o=n[a],S(e,t,a,!1,!1)&&(e.dump.length>1024&&(s+="? "),s+=e.dump+": ",S(e,t,o,!1,!1)&&(s+=e.dump,l+=s));e.tag=u,e.dump="{"+l+"}"}function O(e,t,n,r){var i,a,o,l,u,c,p="",h=e.tag,f=Object.keys(n);if(e.sortKeys===!0)f.sort();else if("function"==typeof e.sortKeys)f.sort(e.sortKeys);else if(e.sortKeys)throw new T("sortKeys must be a boolean or a function");for(i=0,a=f.length;a>i;i+=1)c="",r&&0===i||(c+=s(e,t)),o=f[i],l=n[o],S(e,t+1,o,!0,!0,!0)&&(u=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024,u&&(c+=e.dump&&L===e.dump.charCodeAt(0)?"?":"? "),c+=e.dump,u&&(c+=s(e,t)),S(e,t+1,l,!0,u)&&(c+=e.dump&&L===e.dump.charCodeAt(0)?":":": ",c+=e.dump,p+=c));e.tag=h,e.dump=p||"{}"}function A(e,t,n){var r,i,a,o,s,l;for(i=n?e.explicitTypes:e.implicitTypes,a=0,o=i.length;o>a;a+=1)if(s=i[a],(s.instanceOf||s.predicate)&&(!s.instanceOf||"object"==typeof t&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(e.tag=n?s.tag:"?",s.represent){if(l=e.styleMap[s.tag]||s.defaultStyle,"[object Function]"===U.call(s.represent))r=s.represent(t,l);else{if(!D.call(s.represent,l))throw new T("!<"+s.tag+'> tag resolver accepts not "'+l+'" style');r=s.represent[l](t,l)}e.dump=r}return!0}return!1}function S(e,t,n,r,i,a){e.tag=null,e.dump=n,A(e,n,!1)||A(e,n,!0);var o=U.call(e.dump);r&&(r=e.flowLevel<0||e.flowLevel>t);var s,l,u="[object Object]"===o||"[object Array]"===o;if(u&&(s=e.duplicates.indexOf(n),l=-1!==s),(null!==e.tag&&"?"!==e.tag||l||2!==e.indent&&t>0)&&(i=!1),l&&e.usedDuplicates[s])e.dump="*ref_"+s;else{if(u&&l&&!e.usedDuplicates[s]&&(e.usedDuplicates[s]=!0),"[object Object]"===o)r&&0!==Object.keys(e.dump).length?(O(e,t,e.dump,i),l&&(e.dump="&ref_"+s+e.dump)):(j(e,t,e.dump),l&&(e.dump="&ref_"+s+" "+e.dump));else if("[object Array]"===o)r&&0!==e.dump.length?(x(e,t,e.dump,i),l&&(e.dump="&ref_"+s+e.dump)):(w(e,t,e.dump),l&&(e.dump="&ref_"+s+" "+e.dump));else{if("[object String]"!==o){if(e.skipInvalid)return!1;throw new T("unacceptable kind of an object to dump "+o)}"?"!==e.tag&&d(e,e.dump,t,a)}null!==e.tag&&"?"!==e.tag&&(e.dump="!<"+e.tag+"> "+e.dump)}return!0}function _(e,t){var n,r,i=[],a=[];for(k(e,i,a),n=0,r=a.length;r>n;n+=1)t.duplicates.push(i[a[n]]);t.usedDuplicates=new Array(r)}function k(e,t,n){var r,i,a;if(null!==e&&"object"==typeof e)if(i=t.indexOf(e),-1!==i)-1===n.indexOf(i)&&n.push(i);else if(t.push(e),Array.isArray(e))for(i=0,a=e.length;a>i;i+=1)k(e[i],t,n);else for(r=Object.keys(e),i=0,a=r.length;a>i;i+=1)k(e[r[i]],t,n)}function E(e,t){t=t||{};var n=new a(t);return n.noRefs||_(e,n),S(n,0,e,!0,!0)?n.dump+"\n":""}function C(e,t){return E(e,I.extend({schema:$},t))}var I=e("./common"),T=e("./exception"),M=e("./schema/default_full"),$=e("./schema/default_safe"),U=Object.prototype.toString,D=Object.prototype.hasOwnProperty,P=9,L=10,R=32,N=33,F=34,B=35,q=37,V=38,z=39,H=42,J=44,Y=45,Q=58,K=62,W=63,X=64,G=91,Z=93,ee=96,te=123,ne=124,re=125,ie={};ie[0]="\\0",ie[7]="\\a",ie[8]="\\b",ie[9]="\\t",ie[10]="\\n",ie[11]="\\v",ie[12]="\\f",ie[13]="\\r",ie[27]="\\e",ie[34]='\\"',ie[92]="\\\\",ie[133]="\\N",ie[160]="\\_",ie[8232]="\\L",ie[8233]="\\P";var ae=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],oe=1,se=2,le=3,ue=4,ce=5;t.exports.dump=E,t.exports.safeDump=C},{"./common":21,"./exception":23,"./schema/default_full":28,"./schema/default_safe":29}],23:[function(e,t,n){"use strict";function r(e,t){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||"",this.name="YAMLException",this.reason=e,this.mark=t,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():"")}r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r.prototype.toString=function(e){var t=this.name+": ";return t+=this.reason||"(unknown reason)",!e&&this.mark&&(t+=" "+this.mark.toString()),t},t.exports=r},{}],24:[function(e,t,n){"use strict";function r(e){return 10===e||13===e}function i(e){return 9===e||32===e}function a(e){return 9===e||32===e||10===e||13===e}function o(e){return 44===e||91===e||93===e||123===e||125===e}function s(e){var t;return e>=48&&57>=e?e-48:(t=32|e,t>=97&&102>=t?t-97+10:-1)}function l(e){return 120===e?2:117===e?4:85===e?8:0}function u(e){return e>=48&&57>=e?e-48:-1}function c(e){return 48===e?"\x00":97===e?"":98===e?"\b":116===e?"	":9===e?"	":110===e?"\n":118===e?"\x0B":102===e?"\f":114===e?"\r":101===e?"":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"":95===e?" ":76===e?"\u2028":80===e?"\u2029":""}function p(e){return 65535>=e?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}function h(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||z,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function f(e,t){return new B(t,new q(e.filename,e.input,e.position,e.line,e.position-e.lineStart))}function d(e,t){throw f(e,t)}function m(e,t){e.onWarning&&e.onWarning.call(null,f(e,t))}function y(e,t,n,r){var i,a,o,s;if(n>t){if(s=e.input.slice(t,n),r)for(i=0,a=s.length;a>i;i+=1)o=s.charCodeAt(i),9===o||o>=32&&1114111>=o||d(e,"expected valid JSON character");else Z.test(s)&&d(e,"the stream contains non-printable characters");e.result+=s}}function g(e,t,n,r){var i,a,o,s;for(F.isObject(n)||d(e,"cannot merge mappings; the provided source object is unacceptable"),i=Object.keys(n),o=0,s=i.length;s>o;o+=1)a=i[o],H.call(t,a)||(t[a]=n[a],r[a]=!0)}function v(e,t,n,r,i,a){var o,s;if(i=String(i),null===t&&(t={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(a))for(o=0,s=a.length;s>o;o+=1)g(e,t,a[o],n);else g(e,t,a,n);else e.json||H.call(n,i)||!H.call(t,i)||d(e,"duplicated mapping key"),t[i]=a,delete n[i];return t}function b(e){var t;t=e.input.charCodeAt(e.position),10===t?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):d(e,"a line break is expected"),e.line+=1,e.lineStart=e.position}function w(e,t,n){for(var a=0,o=e.input.charCodeAt(e.position);0!==o;){for(;i(o);)o=e.input.charCodeAt(++e.position);if(t&&35===o)do o=e.input.charCodeAt(++e.position);while(10!==o&&13!==o&&0!==o);if(!r(o))break;for(b(e),o=e.input.charCodeAt(e.position),a++,e.lineIndent=0;32===o;)e.lineIndent++,o=e.input.charCodeAt(++e.position)}return-1!==n&&0!==a&&e.lineIndent<n&&m(e,"deficient indentation"),a}function x(e){var t,n=e.position;return t=e.input.charCodeAt(n),(45===t||46===t)&&t===e.input.charCodeAt(n+1)&&t===e.input.charCodeAt(n+2)&&(n+=3,t=e.input.charCodeAt(n),0===t||a(t))}function j(e,t){1===t?e.result+=" ":t>1&&(e.result+=F.repeat("\n",t-1))}function O(e,t,n){var s,l,u,c,p,h,f,d,m,g=e.kind,v=e.result;if(m=e.input.charCodeAt(e.position),a(m)||o(m)||35===m||38===m||42===m||33===m||124===m||62===m||39===m||34===m||37===m||64===m||96===m)return!1;if((63===m||45===m)&&(l=e.input.charCodeAt(e.position+1),a(l)||n&&o(l)))return!1;for(e.kind="scalar",e.result="",u=c=e.position,p=!1;0!==m;){if(58===m){if(l=e.input.charCodeAt(e.position+1),a(l)||n&&o(l))break}else if(35===m){if(s=e.input.charCodeAt(e.position-1),a(s))break}else{if(e.position===e.lineStart&&x(e)||n&&o(m))break;if(r(m)){if(h=e.line,f=e.lineStart,d=e.lineIndent,w(e,!1,-1),e.lineIndent>=t){p=!0,m=e.input.charCodeAt(e.position);continue}e.position=c,e.line=h,e.lineStart=f,e.lineIndent=d;break}}p&&(y(e,u,c,!1),j(e,e.line-h),u=c=e.position,p=!1),i(m)||(c=e.position+1),m=e.input.charCodeAt(++e.position)}return y(e,u,c,!1),e.result?!0:(e.kind=g,e.result=v,!1)}function A(e,t){var n,i,a;if(n=e.input.charCodeAt(e.position),39!==n)return!1;for(e.kind="scalar",e.result="",e.position++,i=a=e.position;0!==(n=e.input.charCodeAt(e.position));)if(39===n){if(y(e,i,e.position,!0),n=e.input.charCodeAt(++e.position),39!==n)return!0;i=a=e.position,e.position++}else r(n)?(y(e,i,a,!0),j(e,w(e,!1,t)),i=a=e.position):e.position===e.lineStart&&x(e)?d(e,"unexpected end of the document within a single quoted scalar"):(e.position++,a=e.position);d(e,"unexpected end of the stream within a single quoted scalar")}function S(e,t){var n,i,a,o,u,c;if(c=e.input.charCodeAt(e.position),34!==c)return!1;for(e.kind="scalar",e.result="",e.position++,n=i=e.position;0!==(c=e.input.charCodeAt(e.position));){if(34===c)return y(e,n,e.position,!0),e.position++,!0;if(92===c){if(y(e,n,e.position,!0),c=e.input.charCodeAt(++e.position),r(c))w(e,!1,t);else if(256>c&&ie[c])e.result+=ae[c],e.position++;else if((u=l(c))>0){for(a=u,o=0;a>0;a--)c=e.input.charCodeAt(++e.position),(u=s(c))>=0?o=(o<<4)+u:d(e,"expected hexadecimal character");e.result+=p(o),e.position++}else d(e,"unknown escape sequence");n=i=e.position}else r(c)?(y(e,n,i,!0),j(e,w(e,!1,t)),n=i=e.position):e.position===e.lineStart&&x(e)?d(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}d(e,"unexpected end of the stream within a double quoted scalar")}function _(e,t){var n,r,i,o,s,l,u,c,p,h,f,m=!0,y=e.tag,g=e.anchor,b={};if(f=e.input.charCodeAt(e.position),91===f)o=93,u=!1,r=[];else{if(123!==f)return!1;o=125,u=!0,r={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=r),f=e.input.charCodeAt(++e.position);0!==f;){if(w(e,!0,t),f=e.input.charCodeAt(e.position),f===o)return e.position++,e.tag=y,e.anchor=g,e.kind=u?"mapping":"sequence",e.result=r,!0;m||d(e,"missed comma between flow collection entries"),p=c=h=null,s=l=!1,63===f&&(i=e.input.charCodeAt(e.position+1),a(i)&&(s=l=!0,e.position++,w(e,!0,t))),n=e.line,$(e,t,J,!1,!0),p=e.tag,c=e.result,w(e,!0,t),f=e.input.charCodeAt(e.position),!l&&e.line!==n||58!==f||(s=!0,f=e.input.charCodeAt(++e.position),w(e,!0,t),$(e,t,J,!1,!0),h=e.result),u?v(e,r,b,p,c,h):s?r.push(v(e,null,b,p,c,h)):r.push(c),w(e,!0,t),f=e.input.charCodeAt(e.position),44===f?(m=!0,f=e.input.charCodeAt(++e.position)):m=!1}d(e,"unexpected end of the stream within a flow collection")}function k(e,t){var n,a,o,s,l=W,c=!1,p=!1,h=t,f=0,m=!1;if(s=e.input.charCodeAt(e.position),124===s)a=!1;else{if(62!==s)return!1;a=!0}for(e.kind="scalar",e.result="";0!==s;)if(s=e.input.charCodeAt(++e.position),43===s||45===s)W===l?l=43===s?G:X:d(e,"repeat of a chomping mode identifier");else{if(!((o=u(s))>=0))break;0===o?d(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):p?d(e,"repeat of an indentation width identifier"):(h=t+o-1,p=!0)}if(i(s)){do s=e.input.charCodeAt(++e.position);while(i(s));if(35===s)do s=e.input.charCodeAt(++e.position);while(!r(s)&&0!==s)}for(;0!==s;){for(b(e),e.lineIndent=0,s=e.input.charCodeAt(e.position);(!p||e.lineIndent<h)&&32===s;)e.lineIndent++,s=e.input.charCodeAt(++e.position);if(!p&&e.lineIndent>h&&(h=e.lineIndent),r(s))f++;else{if(e.lineIndent<h){l===G?e.result+=F.repeat("\n",c?1+f:f):l===W&&c&&(e.result+="\n");break}for(a?i(s)?(m=!0,e.result+=F.repeat("\n",c?1+f:f)):m?(m=!1,e.result+=F.repeat("\n",f+1)):0===f?c&&(e.result+=" "):e.result+=F.repeat("\n",f):e.result+=F.repeat("\n",c?1+f:f),c=!0,p=!0,f=0,n=e.position;!r(s)&&0!==s;)s=e.input.charCodeAt(++e.position);y(e,n,e.position,!1)}}return!0}function E(e,t){var n,r,i,o=e.tag,s=e.anchor,l=[],u=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=l),i=e.input.charCodeAt(e.position);0!==i&&45===i&&(r=e.input.charCodeAt(e.position+1),a(r));)if(u=!0,e.position++,w(e,!0,-1)&&e.lineIndent<=t)l.push(null),i=e.input.charCodeAt(e.position);else if(n=e.line,$(e,t,Q,!1,!0),l.push(e.result),w(e,!0,-1),i=e.input.charCodeAt(e.position),(e.line===n||e.lineIndent>t)&&0!==i)d(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break;return u?(e.tag=o,e.anchor=s,e.kind="sequence",e.result=l,!0):!1}function C(e,t,n){var r,o,s,l,u=e.tag,c=e.anchor,p={},h={},f=null,m=null,y=null,g=!1,b=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=p),l=e.input.charCodeAt(e.position);0!==l;){if(r=e.input.charCodeAt(e.position+1),s=e.line,63!==l&&58!==l||!a(r)){if(!$(e,n,Y,!1,!0))break;if(e.line===s){for(l=e.input.charCodeAt(e.position);i(l);)l=e.input.charCodeAt(++e.position);if(58===l)l=e.input.charCodeAt(++e.position),a(l)||d(e,"a whitespace character is expected after the key-value separator within a block mapping"),g&&(v(e,p,h,f,m,null),f=m=y=null),b=!0,g=!1,o=!1,f=e.tag,m=e.result;else{if(!b)return e.tag=u,e.anchor=c,!0;d(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!b)return e.tag=u,e.anchor=c,!0;d(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===l?(g&&(v(e,p,h,f,m,null),f=m=y=null),b=!0,g=!0,o=!0):g?(g=!1,o=!0):d(e,"incomplete explicit mapping pair; a key node is missed"),e.position+=1,l=r;if((e.line===s||e.lineIndent>t)&&($(e,t,K,!0,o)&&(g?m=e.result:y=e.result),g||(v(e,p,h,f,m,y),f=m=y=null),w(e,!0,-1),l=e.input.charCodeAt(e.position)),e.lineIndent>t&&0!==l)d(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return g&&v(e,p,h,f,m,null),b&&(e.tag=u,e.anchor=c,e.kind="mapping",e.result=p),b}function I(e){var t,n,r,i,o=!1,s=!1;if(i=e.input.charCodeAt(e.position),33!==i)return!1;if(null!==e.tag&&d(e,"duplication of a tag property"),i=e.input.charCodeAt(++e.position),60===i?(o=!0,i=e.input.charCodeAt(++e.position)):33===i?(s=!0,n="!!",i=e.input.charCodeAt(++e.position)):n="!",t=e.position,o){do i=e.input.charCodeAt(++e.position);while(0!==i&&62!==i);e.position<e.length?(r=e.input.slice(t,e.position),i=e.input.charCodeAt(++e.position)):d(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==i&&!a(i);)33===i&&(s?d(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),ne.test(n)||d(e,"named tag handle cannot contain such characters"),s=!0,t=e.position+1)),i=e.input.charCodeAt(++e.position);r=e.input.slice(t,e.position),te.test(r)&&d(e,"tag suffix cannot contain flow indicator characters")}return r&&!re.test(r)&&d(e,"tag name cannot contain such characters: "+r),o?e.tag=r:H.call(e.tagMap,n)?e.tag=e.tagMap[n]+r:"!"===n?e.tag="!"+r:"!!"===n?e.tag="tag:yaml.org,2002:"+r:d(e,'undeclared tag handle "'+n+'"'),!0}function T(e){var t,n;if(n=e.input.charCodeAt(e.position),38!==n)return!1;for(null!==e.anchor&&d(e,"duplication of an anchor property"),n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!a(n)&&!o(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&d(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function M(e){var t,n,r;if(r=e.input.charCodeAt(e.position),42!==r)return!1;for(r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!a(r)&&!o(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&d(e,"name of an alias node must contain at least one character"),n=e.input.slice(t,e.position),e.anchorMap.hasOwnProperty(n)||d(e,'unidentified alias "'+n+'"'),e.result=e.anchorMap[n],w(e,!0,-1),!0}function $(e,t,n,r,i){var a,o,s,l,u,c,p,h,f=1,m=!1,y=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,a=o=s=K===n||Q===n,r&&w(e,!0,-1)&&(m=!0,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)),1===f)for(;I(e)||T(e);)w(e,!0,-1)?(m=!0,s=a,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)):s=!1;if(s&&(s=m||i),1!==f&&K!==n||(p=J===n||Y===n?t:t+1,h=e.position-e.lineStart,1===f?s&&(E(e,h)||C(e,h,p))||_(e,p)?y=!0:(o&&k(e,p)||A(e,p)||S(e,p)?y=!0:M(e)?(y=!0,null===e.tag&&null===e.anchor||d(e,"alias node should not have any properties")):O(e,p,J===n)&&(y=!0,null===e.tag&&(e.tag="?")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===f&&(y=s&&E(e,h))),null!==e.tag&&"!"!==e.tag)if("?"===e.tag){for(l=0,u=e.implicitTypes.length;u>l;l+=1)if(c=e.implicitTypes[l],c.resolve(e.result)){e.result=c.construct(e.result),e.tag=c.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else H.call(e.typeMap,e.tag)?(c=e.typeMap[e.tag],null!==e.result&&c.kind!==e.kind&&d(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+c.kind+'", not "'+e.kind+'"'),c.resolve(e.result)?(e.result=c.construct(e.result),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):d(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")):d(e,"unknown tag !<"+e.tag+">");return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||y}function U(e){var t,n,o,s,l=e.position,u=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap={},e.anchorMap={};0!==(s=e.input.charCodeAt(e.position))&&(w(e,!0,-1),s=e.input.charCodeAt(e.position),!(e.lineIndent>0||37!==s));){for(u=!0,s=e.input.charCodeAt(++e.position),t=e.position;0!==s&&!a(s);)s=e.input.charCodeAt(++e.position);for(n=e.input.slice(t,e.position),o=[],n.length<1&&d(e,"directive name must not be less than one character in length");0!==s;){for(;i(s);)s=e.input.charCodeAt(++e.position);if(35===s){do s=e.input.charCodeAt(++e.position);while(0!==s&&!r(s));break}if(r(s))break;for(t=e.position;0!==s&&!a(s);)s=e.input.charCodeAt(++e.position);o.push(e.input.slice(t,e.position))}0!==s&&b(e),H.call(se,n)?se[n](e,n,o):m(e,'unknown document directive "'+n+'"')}return w(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,w(e,!0,-1)):u&&d(e,"directives end mark is expected"),$(e,e.lineIndent-1,K,!1,!0),w(e,!0,-1),e.checkLineBreaks&&ee.test(e.input.slice(l,e.position))&&m(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&x(e)?void(46===e.input.charCodeAt(e.position)&&(e.position+=3,w(e,!0,-1))):void(e.position<e.length-1&&d(e,"end of the stream or a document separator is expected"))}function D(e,t){e=String(e),t=t||{},0!==e.length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var n=new h(e,t);for(n.input+="\x00";32===n.input.charCodeAt(n.position);)n.lineIndent+=1,n.position+=1;for(;n.position<n.length-1;)U(n);return n.documents}function P(e,t,n){var r,i,a=D(e,n);for(r=0,i=a.length;i>r;r+=1)t(a[r])}function L(e,t){var n=D(e,t);if(0!==n.length){if(1===n.length)return n[0];throw new B("expected a single document in the stream, but found more")}}function R(e,t,n){P(e,t,F.extend({schema:V},n))}function N(e,t){return L(e,F.extend({schema:V},t))}for(var F=e("./common"),B=e("./exception"),q=e("./mark"),V=e("./schema/default_safe"),z=e("./schema/default_full"),H=Object.prototype.hasOwnProperty,J=1,Y=2,Q=3,K=4,W=1,X=2,G=3,Z=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,ee=/[\x85\u2028\u2029]/,te=/[,\[\]\{\}]/,ne=/^(?:!|!!|![a-z\-]+!)$/i,re=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i,ie=new Array(256),ae=new Array(256),oe=0;256>oe;oe++)ie[oe]=c(oe)?1:0,ae[oe]=c(oe);var se={YAML:function(e,t,n){var r,i,a;null!==e.version&&d(e,"duplication of %YAML directive"),1!==n.length&&d(e,"YAML directive accepts exactly one argument"),r=/^([0-9]+)\.([0-9]+)$/.exec(n[0]),null===r&&d(e,"ill-formed argument of the YAML directive"),i=parseInt(r[1],10),a=parseInt(r[2],10),1!==i&&d(e,"unacceptable YAML version of the document"),e.version=n[0],e.checkLineBreaks=2>a,1!==a&&2!==a&&m(e,"unsupported YAML version of the document")},TAG:function(e,t,n){var r,i;2!==n.length&&d(e,"TAG directive accepts exactly two arguments"),r=n[0],i=n[1],ne.test(r)||d(e,"ill-formed tag handle (first argument) of the TAG directive"),H.call(e.tagMap,r)&&d(e,'there is a previously declared suffix for "'+r+'" tag handle'),re.test(i)||d(e,"ill-formed tag prefix (second argument) of the TAG directive"),e.tagMap[r]=i}};t.exports.loadAll=P,t.exports.load=L,t.exports.safeLoadAll=R,t.exports.safeLoad=N},{"./common":21,"./exception":23,"./mark":25,"./schema/default_full":28,"./schema/default_safe":29}],25:[function(e,t,n){"use strict";function r(e,t,n,r,i){this.name=e,this.buffer=t,this.position=n,this.line=r,this.column=i}var i=e("./common");r.prototype.getSnippet=function(e,t){var n,r,a,o,s;if(!this.buffer)return null;for(e=e||4,t=t||75,n="",r=this.position;r>0&&-1==="\x00\r\n\u2028\u2029".indexOf(this.buffer.charAt(r-1));)if(r-=1,this.position-r>t/2-1){n=" ... ",r+=5;break}for(a="",o=this.position;o<this.buffer.length&&-1==="\x00\r\n\u2028\u2029".indexOf(this.buffer.charAt(o));)if(o+=1,o-this.position>t/2-1){a=" ... ",o-=5;break}return s=this.buffer.slice(r,o),i.repeat(" ",e)+n+s+a+"\n"+i.repeat(" ",e+this.position-r+n.length)+"^"},r.prototype.toString=function(e){var t,n="";return this.name&&(n+='in "'+this.name+'" '),n+="at line "+(this.line+1)+", column "+(this.column+1),e||(t=this.getSnippet(),t&&(n+=":\n"+t)),n},t.exports=r},{"./common":21}],26:[function(e,t,n){"use strict";function r(e,t,n){var i=[];return e.include.forEach(function(e){n=r(e,t,n)}),e[t].forEach(function(e){n.forEach(function(t,n){t.tag===e.tag&&i.push(n)}),n.push(e)}),n.filter(function(e,t){return-1===i.indexOf(t)})}function i(){function e(e){r[e.tag]=e}var t,n,r={};for(t=0,n=arguments.length;n>t;t+=1)arguments[t].forEach(e);return r}function a(e){this.include=e.include||[],this.implicit=e.implicit||[],this.explicit=e.explicit||[],this.implicit.forEach(function(e){if(e.loadKind&&"scalar"!==e.loadKind)throw new s("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=r(this,"implicit",[]),this.compiledExplicit=r(this,"explicit",[]),this.compiledTypeMap=i(this.compiledImplicit,this.compiledExplicit)}var o=e("./common"),s=e("./exception"),l=e("./type");a.DEFAULT=null,a.create=function(){var e,t;switch(arguments.length){case 1:e=a.DEFAULT,t=arguments[0];break;case 2:e=arguments[0],t=arguments[1];break;default:throw new s("Wrong number of arguments for Schema.create function")}if(e=o.toArray(e),t=o.toArray(t),!e.every(function(e){return e instanceof a}))throw new s("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!t.every(function(e){return e instanceof l}))throw new s("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new a({include:e,explicit:t})},t.exports=a},{"./common":21,"./exception":23,"./type":32}],27:[function(e,t,n){"use strict";var r=e("../schema");t.exports=new r({include:[e("./json")]})},{"../schema":26,"./json":31}],28:[function(e,t,n){"use strict";var r=e("../schema");t.exports=r.DEFAULT=new r({include:[e("./default_safe")],explicit:[e("../type/js/undefined"),e("../type/js/regexp"),e("../type/js/function")]})},{"../schema":26,"../type/js/function":37,"../type/js/regexp":38,"../type/js/undefined":39,"./default_safe":29}],29:[function(e,t,n){"use strict";var r=e("../schema");t.exports=new r({include:[e("./core")],implicit:[e("../type/timestamp"),e("../type/merge")],explicit:[e("../type/binary"),e("../type/omap"),e("../type/pairs"),e("../type/set")]})},{"../schema":26,"../type/binary":33,"../type/merge":41,"../type/omap":43,"../type/pairs":44,"../type/set":46,"../type/timestamp":48,"./core":27}],30:[function(e,t,n){"use strict";var r=e("../schema");t.exports=new r({explicit:[e("../type/str"),e("../type/seq"),e("../type/map")]})},{"../schema":26,"../type/map":40,"../type/seq":45,"../type/str":47}],31:[function(e,t,n){"use strict";var r=e("../schema");t.exports=new r({include:[e("./failsafe")],implicit:[e("../type/null"),e("../type/bool"),e("../type/int"),e("../type/float")]})},{"../schema":26,"../type/bool":34,"../type/float":35,"../type/int":36,"../type/null":42,"./failsafe":30}],32:[function(e,t,n){"use strict";function r(e){var t={};return null!==e&&Object.keys(e).forEach(function(n){e[n].forEach(function(e){t[String(e)]=n})}),t}function i(e,t){if(t=t||{},Object.keys(t).forEach(function(t){if(-1===o.indexOf(t))throw new a('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')}),this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.defaultStyle=t.defaultStyle||null,this.styleAliases=r(t.styleAliases||null),-1===s.indexOf(this.kind))throw new a('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var a=e("./exception"),o=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],s=["scalar","sequence","mapping"];t.exports=i},{"./exception":23}],33:[function(e,t,n){"use strict";function r(e){if(null===e)return!1;var t,n,r=0,i=e.length,a=p;for(n=0;i>n;n++)if(t=a.indexOf(e.charAt(n)),!(t>64)){if(0>t)return!1;r+=6}return r%8===0}function i(e){var t,n,r=e.replace(/[\r\n=]/g,""),i=r.length,a=p,o=0,l=[];for(t=0;i>t;t++)t%4===0&&t&&(l.push(o>>16&255),l.push(o>>8&255),l.push(255&o)),o=o<<6|a.indexOf(r.charAt(t));return n=i%4*6,0===n?(l.push(o>>16&255),l.push(o>>8&255),l.push(255&o)):18===n?(l.push(o>>10&255),l.push(o>>2&255)):12===n&&l.push(o>>4&255),s?new s(l):l}function a(e){var t,n,r="",i=0,a=e.length,o=p;for(t=0;a>t;t++)t%3===0&&t&&(r+=o[i>>18&63],r+=o[i>>12&63],r+=o[i>>6&63],r+=o[63&i]),i=(i<<8)+e[t];return n=a%3,0===n?(r+=o[i>>18&63],r+=o[i>>12&63],r+=o[i>>6&63],r+=o[63&i]):2===n?(r+=o[i>>10&63],r+=o[i>>4&63],r+=o[i<<2&63],r+=o[64]):1===n&&(r+=o[i>>2&63],r+=o[i<<4&63],r+=o[64],r+=o[64]),r}function o(e){return s&&s.isBuffer(e)}var s;try{var l=e;s=l("buffer").Buffer}catch(u){}var c=e("../type"),p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";t.exports=new c("tag:yaml.org,2002:binary",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:a})},{"../type":32}],34:[function(e,t,n){"use strict";function r(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)}function i(e){return"true"===e||"True"===e||"TRUE"===e}function a(e){return"[object Boolean]"===Object.prototype.toString.call(e)}var o=e("../type");t.exports=new o("tag:yaml.org,2002:bool",{kind:"scalar",resolve:r,construct:i,predicate:a,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})},{"../type":32}],35:[function(e,t,n){"use strict";function r(e){return null===e?!1:!!u.test(e)}function i(e){var t,n,r,i;return t=e.replace(/_/g,"").toLowerCase(),n="-"===t[0]?-1:1,i=[],"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t?1===n?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:t.indexOf(":")>=0?(t.split(":").forEach(function(e){
i.unshift(parseFloat(e,10))}),t=0,r=1,i.forEach(function(e){t+=e*r,r*=60}),n*t):n*parseFloat(t,10)}function a(e,t){var n;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(s.isNegativeZero(e))return"-0.0";return n=e.toString(10),c.test(n)?n.replace("e",".e"):n}function o(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!==0||s.isNegativeZero(e))}var s=e("../common"),l=e("../type"),u=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)\\.[0-9_]*(?:[eE][-+][0-9]+)?|\\.[0-9_]+(?:[eE][-+][0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),c=/^[-+]?[0-9]+e/;t.exports=new l("tag:yaml.org,2002:float",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:a,defaultStyle:"lowercase"})},{"../common":21,"../type":32}],36:[function(e,t,n){"use strict";function r(e){return e>=48&&57>=e||e>=65&&70>=e||e>=97&&102>=e}function i(e){return e>=48&&55>=e}function a(e){return e>=48&&57>=e}function o(e){if(null===e)return!1;var t,n=e.length,o=0,s=!1;if(!n)return!1;if(t=e[o],"-"!==t&&"+"!==t||(t=e[++o]),"0"===t){if(o+1===n)return!0;if(t=e[++o],"b"===t){for(o++;n>o;o++)if(t=e[o],"_"!==t){if("0"!==t&&"1"!==t)return!1;s=!0}return s}if("x"===t){for(o++;n>o;o++)if(t=e[o],"_"!==t){if(!r(e.charCodeAt(o)))return!1;s=!0}return s}for(;n>o;o++)if(t=e[o],"_"!==t){if(!i(e.charCodeAt(o)))return!1;s=!0}return s}for(;n>o;o++)if(t=e[o],"_"!==t){if(":"===t)break;if(!a(e.charCodeAt(o)))return!1;s=!0}return s?":"!==t?!0:/^(:[0-5]?[0-9])+$/.test(e.slice(o)):!1}function s(e){var t,n,r=e,i=1,a=[];return-1!==r.indexOf("_")&&(r=r.replace(/_/g,"")),t=r[0],"-"!==t&&"+"!==t||("-"===t&&(i=-1),r=r.slice(1),t=r[0]),"0"===r?0:"0"===t?"b"===r[1]?i*parseInt(r.slice(2),2):"x"===r[1]?i*parseInt(r,16):i*parseInt(r,8):-1!==r.indexOf(":")?(r.split(":").forEach(function(e){a.unshift(parseInt(e,10))}),r=0,n=1,a.forEach(function(e){r+=e*n,n*=60}),i*r):i*parseInt(r,10)}function l(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1===0&&!u.isNegativeZero(e)}var u=e("../common"),c=e("../type");t.exports=new c("tag:yaml.org,2002:int",{kind:"scalar",resolve:o,construct:s,predicate:l,represent:{binary:function(e){return"0b"+e.toString(2)},octal:function(e){return"0"+e.toString(8)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return"0x"+e.toString(16).toUpperCase()}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},{"../common":21,"../type":32}],37:[function(e,t,n){"use strict";function r(e){if(null===e)return!1;try{var t="("+e+")",n=s.parse(t,{range:!0});return"Program"===n.type&&1===n.body.length&&"ExpressionStatement"===n.body[0].type&&"FunctionExpression"===n.body[0].expression.type}catch(r){return!1}}function i(e){var t,n="("+e+")",r=s.parse(n,{range:!0}),i=[];if("Program"!==r.type||1!==r.body.length||"ExpressionStatement"!==r.body[0].type||"FunctionExpression"!==r.body[0].expression.type)throw new Error("Failed to resolve function");return r.body[0].expression.params.forEach(function(e){i.push(e.name)}),t=r.body[0].expression.body.range,new Function(i,n.slice(t[0]+1,t[1]-1))}function a(e){return e.toString()}function o(e){return"[object Function]"===Object.prototype.toString.call(e)}var s;try{var l=e;s=l("esprima")}catch(u){"undefined"!=typeof window&&(s=window.esprima)}var c=e("../../type");t.exports=new c("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:a})},{"../../type":32}],38:[function(e,t,n){"use strict";function r(e){if(null===e)return!1;if(0===e.length)return!1;var t=e,n=/\/([gim]*)$/.exec(e),r="";if("/"===t[0]){if(n&&(r=n[1]),r.length>3)return!1;if("/"!==t[t.length-r.length-1])return!1}return!0}function i(e){var t=e,n=/\/([gim]*)$/.exec(e),r="";return"/"===t[0]&&(n&&(r=n[1]),t=t.slice(1,t.length-r.length-1)),new RegExp(t,r)}function a(e){var t="/"+e.source+"/";return e.global&&(t+="g"),e.multiline&&(t+="m"),e.ignoreCase&&(t+="i"),t}function o(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var s=e("../../type");t.exports=new s("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:a})},{"../../type":32}],39:[function(e,t,n){"use strict";function r(){return!0}function i(){}function a(){return""}function o(e){return"undefined"==typeof e}var s=e("../../type");t.exports=new s("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:a})},{"../../type":32}],40:[function(e,t,n){"use strict";var r=e("../type");t.exports=new r("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}})},{"../type":32}],41:[function(e,t,n){"use strict";function r(e){return"<<"===e||null===e}var i=e("../type");t.exports=new i("tag:yaml.org,2002:merge",{kind:"scalar",resolve:r})},{"../type":32}],42:[function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)}function i(){return null}function a(e){return null===e}var o=e("../type");t.exports=new o("tag:yaml.org,2002:null",{kind:"scalar",resolve:r,construct:i,predicate:a,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},{"../type":32}],43:[function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n,r,i,a,l=[],u=e;for(t=0,n=u.length;n>t;t+=1){if(r=u[t],a=!1,"[object Object]"!==s.call(r))return!1;for(i in r)if(o.call(r,i)){if(a)return!1;a=!0}if(!a)return!1;if(-1!==l.indexOf(i))return!1;l.push(i)}return!0}function i(e){return null!==e?e:[]}var a=e("../type"),o=Object.prototype.hasOwnProperty,s=Object.prototype.toString;t.exports=new a("tag:yaml.org,2002:omap",{kind:"sequence",resolve:r,construct:i})},{"../type":32}],44:[function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n,r,i,a,s=e;for(a=new Array(s.length),t=0,n=s.length;n>t;t+=1){if(r=s[t],"[object Object]"!==o.call(r))return!1;if(i=Object.keys(r),1!==i.length)return!1;a[t]=[i[0],r[i[0]]]}return!0}function i(e){if(null===e)return[];var t,n,r,i,a,o=e;for(a=new Array(o.length),t=0,n=o.length;n>t;t+=1)r=o[t],i=Object.keys(r),a[t]=[i[0],r[i[0]]];return a}var a=e("../type"),o=Object.prototype.toString;t.exports=new a("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:r,construct:i})},{"../type":32}],45:[function(e,t,n){"use strict";var r=e("../type");t.exports=new r("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}})},{"../type":32}],46:[function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n=e;for(t in n)if(o.call(n,t)&&null!==n[t])return!1;return!0}function i(e){return null!==e?e:{}}var a=e("../type"),o=Object.prototype.hasOwnProperty;t.exports=new a("tag:yaml.org,2002:set",{kind:"mapping",resolve:r,construct:i})},{"../type":32}],47:[function(e,t,n){"use strict";var r=e("../type");t.exports=new r("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}})},{"../type":32}],48:[function(e,t,n){"use strict";function r(e){return null===e?!1:null!==s.exec(e)?!0:null!==l.exec(e)}function i(e){var t,n,r,i,a,o,u,c,p,h,f=0,d=null;if(t=s.exec(e),null===t&&(t=l.exec(e)),null===t)throw new Error("Date resolve error");if(n=+t[1],r=+t[2]-1,i=+t[3],!t[4])return new Date(Date.UTC(n,r,i));if(a=+t[4],o=+t[5],u=+t[6],t[7]){for(f=t[7].slice(0,3);f.length<3;)f+="0";f=+f}return t[9]&&(c=+t[10],p=+(t[11]||0),d=6e4*(60*c+p),"-"===t[9]&&(d=-d)),h=new Date(Date.UTC(n,r,i,a,o,u,f)),d&&h.setTime(h.getTime()-d),h}function a(e){return e.toISOString()}var o=e("../type"),s=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),l=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");t.exports=new o("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:r,construct:i,instanceOf:Date,represent:a})},{"../type":32}],49:[function(e,t,n){function r(e,t,n){var r=e?e.length:0;if(!r)return-1;if("number"==typeof n)n=0>n?o(r+n,0):n;else if(n){var s=a(e,t);return r>s&&(t===t?t===e[s]:e[s]!==e[s])?s:-1}return i(e,t,n||0)}var i=e("../internal/baseIndexOf"),a=e("../internal/binaryIndex"),o=Math.max;t.exports=r},{"../internal/baseIndexOf":78,"../internal/binaryIndex":92}],50:[function(e,t,n){function r(e){var t=e?e.length:0;return t?e[t-1]:void 0}t.exports=r},{}],51:[function(e,t,n){function r(e){if(l(e)&&!s(e)&&!(e instanceof i)){if(e instanceof a)return e;if(p.call(e,"__chain__")&&p.call(e,"__wrapped__"))return u(e)}return new a(e)}var i=e("../internal/LazyWrapper"),a=e("../internal/LodashWrapper"),o=e("../internal/baseLodash"),s=e("../lang/isArray"),l=e("../internal/isObjectLike"),u=e("../internal/wrapperClone"),c=Object.prototype,p=c.hasOwnProperty;r.prototype=o.prototype,t.exports=r},{"../internal/LazyWrapper":60,"../internal/LodashWrapper":61,"../internal/baseLodash":82,"../internal/isObjectLike":126,"../internal/wrapperClone":137,"../lang/isArray":140}],52:[function(e,t,n){t.exports=e("./forEach")},{"./forEach":54}],53:[function(e,t,n){var r=e("../internal/baseEach"),i=e("../internal/createFind"),a=i(r);t.exports=a},{"../internal/baseEach":71,"../internal/createFind":102}],54:[function(e,t,n){var r=e("../internal/arrayEach"),i=e("../internal/baseEach"),a=e("../internal/createForEach"),o=a(r,i);t.exports=o},{"../internal/arrayEach":63,"../internal/baseEach":71,"../internal/createForEach":103}],55:[function(e,t,n){function r(e,t,n,r){var h=e?a(e):0;return l(h)||(e=c(e),h=e.length),n="number"!=typeof n||r&&s(t,n,r)?0:0>n?p(h+n,0):n||0,"string"==typeof e||!o(e)&&u(e)?h>=n&&e.indexOf(t,n)>-1:!!h&&i(e,t,n)>-1}var i=e("../internal/baseIndexOf"),a=e("../internal/getLength"),o=e("../lang/isArray"),s=e("../internal/isIterateeCall"),l=e("../internal/isLength"),u=e("../lang/isString"),c=e("../object/values"),p=Math.max;t.exports=r},{"../internal/baseIndexOf":78,"../internal/getLength":112,"../internal/isIterateeCall":122,"../internal/isLength":125,"../lang/isArray":140,"../lang/isString":146,"../object/values":152}],56:[function(e,t,n){function r(e,t,n){var r=s(e)?i:o;return t=a(t,n,3),r(e,t)}var i=e("../internal/arrayMap"),a=e("../internal/baseCallback"),o=e("../internal/baseMap"),s=e("../lang/isArray");t.exports=r},{"../internal/arrayMap":64,"../internal/baseCallback":67,"../internal/baseMap":83,"../lang/isArray":140}],57:[function(e,t,n){var r=e("../internal/getNative"),i=r(Date,"now"),a=i||function(){return(new Date).getTime()};t.exports=a},{"../internal/getNative":114}],58:[function(e,t,n){var r=e("../internal/createWrapper"),i=e("../internal/replaceHolders"),a=e("./restParam"),o=1,s=32,l=a(function(e,t,n){var a=o;if(n.length){var u=i(n,l.placeholder);a|=s}return r(e,a,t,n,u)});l.placeholder={},t.exports=l},{"../internal/createWrapper":106,"../internal/replaceHolders":132,"./restParam":59}],59:[function(e,t,n){function r(e,t){if("function"!=typeof e)throw new TypeError(i);return t=a(void 0===t?e.length-1:+t||0,0),function(){for(var n=arguments,r=-1,i=a(n.length-t,0),o=Array(i);++r<i;)o[r]=n[t+r];switch(t){case 0:return e.call(this,o);case 1:return e.call(this,n[0],o);case 2:return e.call(this,n[0],n[1],o)}var s=Array(t+1);for(r=-1;++r<t;)s[r]=n[r];return s[t]=o,e.apply(this,s)}}var i="Expected a function",a=Math.max;t.exports=r},{}],60:[function(e,t,n){function r(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=o,this.__views__=[]}var i=e("./baseCreate"),a=e("./baseLodash"),o=Number.POSITIVE_INFINITY;r.prototype=i(a.prototype),r.prototype.constructor=r,t.exports=r},{"./baseCreate":70,"./baseLodash":82}],61:[function(e,t,n){function r(e,t,n){this.__wrapped__=e,this.__actions__=n||[],this.__chain__=!!t}var i=e("./baseCreate"),a=e("./baseLodash");r.prototype=i(a.prototype),r.prototype.constructor=r,t.exports=r},{"./baseCreate":70,"./baseLodash":82}],62:[function(e,t,n){function r(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}t.exports=r},{}],63:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length;++n<r&&t(e[n],n,e)!==!1;);return e}t.exports=r},{}],64:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}t.exports=r},{}],65:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}t.exports=r},{}],66:[function(e,t,n){function r(e,t){return null==t?e:i(t,a(t),e)}var i=e("./baseCopy"),a=e("../object/keys");t.exports=r},{"../object/keys":149,"./baseCopy":69}],67:[function(e,t,n){function r(e,t,n){var r=typeof e;return"function"==r?void 0===t?e:o(e,t,n):null==e?s:"object"==r?i(e):void 0===t?l(e):a(e,t)}var i=e("./baseMatches"),a=e("./baseMatchesProperty"),o=e("./bindCallback"),s=e("../utility/identity"),l=e("../utility/property");t.exports=r},{"../utility/identity":154,"../utility/property":156,"./baseMatches":84,"./baseMatchesProperty":85,"./bindCallback":94}],68:[function(e,t,n){function r(e,t,n,m,y,g,v){var w;if(n&&(w=y?n(e,m,y):n(e)),void 0!==w)return w;if(!f(e))return e;var x=p(e);if(x){if(w=l(e),!t)return i(e,w)}else{var O=N.call(e),A=O==b;if(O!=j&&O!=d&&(!A||y))return L[O]?u(e,O,t):y?e:{};if(h(e))return y?e:{};if(w=c(A?{}:e),!t)return o(w,e)}g||(g=[]),v||(v=[]);for(var S=g.length;S--;)if(g[S]==e)return v[S];return g.push(e),v.push(w),(x?a:s)(e,function(i,a){w[a]=r(i,t,n,a,e,g,v)}),w}var i=e("./arrayCopy"),a=e("./arrayEach"),o=e("./baseAssign"),s=e("./baseForOwn"),l=e("./initCloneArray"),u=e("./initCloneByTag"),c=e("./initCloneObject"),p=e("../lang/isArray"),h=e("./isHostObject"),f=e("../lang/isObject"),d="[object Arguments]",m="[object Array]",y="[object Boolean]",g="[object Date]",v="[object Error]",b="[object Function]",w="[object Map]",x="[object Number]",j="[object Object]",O="[object RegExp]",A="[object Set]",S="[object String]",_="[object WeakMap]",k="[object ArrayBuffer]",E="[object Float32Array]",C="[object Float64Array]",I="[object Int8Array]",T="[object Int16Array]",M="[object Int32Array]",$="[object Uint8Array]",U="[object Uint8ClampedArray]",D="[object Uint16Array]",P="[object Uint32Array]",L={};L[d]=L[m]=L[k]=L[y]=L[g]=L[E]=L[C]=L[I]=L[T]=L[M]=L[x]=L[j]=L[O]=L[S]=L[$]=L[U]=L[D]=L[P]=!0,L[v]=L[b]=L[w]=L[A]=L[_]=!1;var R=Object.prototype,N=R.toString;t.exports=r},{"../lang/isArray":140,"../lang/isObject":144,"./arrayCopy":62,"./arrayEach":63,"./baseAssign":66,"./baseForOwn":76,"./initCloneArray":116,"./initCloneByTag":117,"./initCloneObject":118,"./isHostObject":120}],69:[function(e,t,n){function r(e,t,n){n||(n={});for(var r=-1,i=t.length;++r<i;){var a=t[r];n[a]=e[a]}return n}t.exports=r},{}],70:[function(e,t,n){var r=e("../lang/isObject"),i=function(){function e(){}return function(t){if(r(t)){e.prototype=t;var n=new e;e.prototype=void 0}return n||{}}}();t.exports=i},{"../lang/isObject":144}],71:[function(e,t,n){var r=e("./baseForOwn"),i=e("./createBaseEach"),a=i(r);t.exports=a},{"./baseForOwn":76,"./createBaseEach":98}],72:[function(e,t,n){function r(e,t,n,r){var i;return n(e,function(e,n,a){return t(e,n,a)?(i=r?n:e,!1):void 0}),i}t.exports=r},{}],73:[function(e,t,n){function r(e,t,n){for(var r=e.length,i=n?r:-1;n?i--:++i<r;)if(t(e[i],i,e))return i;return-1}t.exports=r},{}],74:[function(e,t,n){var r=e("./createBaseFor"),i=r();t.exports=i},{"./createBaseFor":99}],75:[function(e,t,n){function r(e,t){return i(e,t,a)}var i=e("./baseFor"),a=e("../object/keysIn");t.exports=r},{"../object/keysIn":150,"./baseFor":74}],76:[function(e,t,n){function r(e,t){return i(e,t,a)}var i=e("./baseFor"),a=e("../object/keys");t.exports=r},{"../object/keys":149,"./baseFor":74}],77:[function(e,t,n){function r(e,t,n){if(null!=e){e=i(e),void 0!==n&&n in e&&(t=[n]);for(var r=0,a=t.length;null!=e&&a>r;)e=i(e)[t[r++]];return r&&r==a?e:void 0}}var i=e("./toObject");t.exports=r},{"./toObject":135}],78:[function(e,t,n){function r(e,t,n){if(t!==t)return i(e,n);for(var r=n-1,a=e.length;++r<a;)if(e[r]===t)return r;return-1}var i=e("./indexOfNaN");t.exports=r},{"./indexOfNaN":115}],79:[function(e,t,n){function r(e,t,n,s,l,u){return e===t?!0:null==e||null==t||!a(e)&&!o(t)?e!==e&&t!==t:i(e,t,r,n,s,l,u)}var i=e("./baseIsEqualDeep"),a=e("../lang/isObject"),o=e("./isObjectLike");t.exports=r},{"../lang/isObject":144,"./baseIsEqualDeep":80,"./isObjectLike":126}],80:[function(e,t,n){function r(e,t,n,r,f,y,g){var v=s(e),b=s(t),w=p,x=p;v||(w=m.call(e),w==c?w=h:w!=h&&(v=u(e))),b||(x=m.call(t),x==c?x=h:x!=h&&(b=u(t)));var j=w==h&&!l(e),O=x==h&&!l(t),A=w==x;if(A&&!v&&!j)return a(e,t,w);if(!f){var S=j&&d.call(e,"__wrapped__"),_=O&&d.call(t,"__wrapped__");if(S||_)return n(S?e.value():e,_?t.value():t,r,f,y,g)}if(!A)return!1;y||(y=[]),g||(g=[]);for(var k=y.length;k--;)if(y[k]==e)return g[k]==t;y.push(e),g.push(t);var E=(v?i:o)(e,t,n,r,f,y,g);return y.pop(),g.pop(),E}var i=e("./equalArrays"),a=e("./equalByTag"),o=e("./equalObjects"),s=e("../lang/isArray"),l=e("./isHostObject"),u=e("../lang/isTypedArray"),c="[object Arguments]",p="[object Array]",h="[object Object]",f=Object.prototype,d=f.hasOwnProperty,m=f.toString;t.exports=r},{"../lang/isArray":140,"../lang/isTypedArray":147,"./equalArrays":107,"./equalByTag":108,"./equalObjects":109,"./isHostObject":120}],81:[function(e,t,n){function r(e,t,n){var r=t.length,o=r,s=!n;if(null==e)return!o;for(e=a(e);r--;){var l=t[r];if(s&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++r<o;){l=t[r];var u=l[0],c=e[u],p=l[1];if(s&&l[2]){if(void 0===c&&!(u in e))return!1}else{var h=n?n(c,p,u):void 0;if(!(void 0===h?i(p,c,n,!0):h))return!1}}return!0}var i=e("./baseIsEqual"),a=e("./toObject");t.exports=r},{"./baseIsEqual":79,"./toObject":135}],82:[function(e,t,n){function r(){}t.exports=r},{}],83:[function(e,t,n){function r(e,t){var n=-1,r=a(e)?Array(e.length):[];return i(e,function(e,i,a){r[++n]=t(e,i,a)}),r}var i=e("./baseEach"),a=e("./isArrayLike");t.exports=r},{"./baseEach":71,"./isArrayLike":119}],84:[function(e,t,n){function r(e){var t=a(e);if(1==t.length&&t[0][2]){var n=t[0][0],r=t[0][1];return function(e){return null==e?!1:(e=o(e),e[n]===r&&(void 0!==r||n in e))}}return function(e){return i(e,t)}}var i=e("./baseIsMatch"),a=e("./getMatchData"),o=e("./toObject");t.exports=r},{"./baseIsMatch":81,"./getMatchData":113,"./toObject":135}],85:[function(e,t,n){function r(e,t){var n=s(e),r=l(e)&&u(t),f=e+"";return e=h(e),function(s){if(null==s)return!1;var l=f;if(s=p(s),(n||!r)&&!(l in s)){if(s=1==e.length?s:i(s,o(e,0,-1)),null==s)return!1;l=c(e),s=p(s)}return s[l]===t?void 0!==t||l in s:a(t,s[l],void 0,!0)}}var i=e("./baseGet"),a=e("./baseIsEqual"),o=e("./baseSlice"),s=e("../lang/isArray"),l=e("./isKey"),u=e("./isStrictComparable"),c=e("../array/last"),p=e("./toObject"),h=e("./toPath");t.exports=r},{"../array/last":50,"../lang/isArray":140,"./baseGet":77,"./baseIsEqual":79,"./baseSlice":89,"./isKey":123,"./isStrictComparable":127,"./toObject":135,"./toPath":136}],86:[function(e,t,n){function r(e){return function(t){return null==t?void 0:i(t)[e]}}var i=e("./toObject");t.exports=r},{"./toObject":135}],87:[function(e,t,n){function r(e){var t=e+"";return e=a(e),function(n){return i(n,e,t)}}var i=e("./baseGet"),a=e("./toPath");t.exports=r},{"./baseGet":77,"./toPath":136}],88:[function(e,t,n){var r=e("../utility/identity"),i=e("./metaMap"),a=i?function(e,t){return i.set(e,t),e}:r;t.exports=a},{"../utility/identity":154,"./metaMap":129}],89:[function(e,t,n){function r(e,t,n){var r=-1,i=e.length;t=null==t?0:+t||0,0>t&&(t=-t>i?0:i+t),n=void 0===n||n>i?i:+n||0,0>n&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(i);++r<i;)a[r]=e[r+t];return a}t.exports=r},{}],90:[function(e,t,n){function r(e){return null==e?"":e+""}t.exports=r},{}],91:[function(e,t,n){function r(e,t){for(var n=-1,r=t.length,i=Array(r);++n<r;)i[n]=e[t[n]];return i}t.exports=r},{}],92:[function(e,t,n){function r(e,t,n){var r=0,o=e?e.length:r;if("number"==typeof t&&t===t&&s>=o){for(;o>r;){var l=r+o>>>1,u=e[l];(n?t>=u:t>u)&&null!==u?r=l+1:o=l}return o}return i(e,t,a,n)}var i=e("./binaryIndexBy"),a=e("../utility/identity"),o=4294967295,s=o>>>1;t.exports=r},{"../utility/identity":154,"./binaryIndexBy":93}],93:[function(e,t,n){function r(e,t,n,r){t=n(t);for(var o=0,l=e?e.length:0,u=t!==t,c=null===t,p=void 0===t;l>o;){var h=i((o+l)/2),f=n(e[h]),d=void 0!==f,m=f===f;if(u)var y=m||r;else y=c?m&&d&&(r||null!=f):p?m&&(r||d):null==f?!1:r?t>=f:t>f;y?o=h+1:l=h}return a(l,s)}var i=Math.floor,a=Math.min,o=4294967295,s=o-1;t.exports=r},{}],94:[function(e,t,n){function r(e,t,n){if("function"!=typeof e)return i;if(void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 3:return function(n,r,i){return e.call(t,n,r,i)};case 4:return function(n,r,i,a){return e.call(t,n,r,i,a)};case 5:return function(n,r,i,a,o){return e.call(t,n,r,i,a,o)}}return function(){return e.apply(t,arguments)}}var i=e("../utility/identity");t.exports=r},{"../utility/identity":154}],95:[function(e,t,n){(function(e){function n(e){var t=new r(e.byteLength),n=new i(t);return n.set(new i(e)),t}var r=e.ArrayBuffer,i=e.Uint8Array;t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],96:[function(e,t,n){function r(e,t,n){for(var r=n.length,a=-1,o=i(e.length-r,0),s=-1,l=t.length,u=Array(l+o);++s<l;)u[s]=t[s];for(;++a<r;)u[n[a]]=e[a];for(;o--;)u[s++]=e[a++];return u}var i=Math.max;t.exports=r},{}],97:[function(e,t,n){function r(e,t,n){for(var r=-1,a=n.length,o=-1,s=i(e.length-a,0),l=-1,u=t.length,c=Array(s+u);++o<s;)c[o]=e[o];for(var p=o;++l<u;)c[p+l]=t[l];for(;++r<a;)c[p+n[r]]=e[o++];return c}var i=Math.max;t.exports=r},{}],98:[function(e,t,n){function r(e,t){return function(n,r){var s=n?i(n):0;if(!a(s))return e(n,r);for(var l=t?s:-1,u=o(n);(t?l--:++l<s)&&r(u[l],l,u)!==!1;);return n}}var i=e("./getLength"),a=e("./isLength"),o=e("./toObject");t.exports=r},{"./getLength":112,"./isLength":125,"./toObject":135}],99:[function(e,t,n){function r(e){return function(t,n,r){for(var a=i(t),o=r(t),s=o.length,l=e?s:-1;e?l--:++l<s;){var u=o[l];if(n(a[u],u,a)===!1)break}return t}}var i=e("./toObject");t.exports=r},{"./toObject":135}],100:[function(e,t,n){(function(n){function r(e,t){function r(){var i=this&&this!==n&&this instanceof r?a:e;return i.apply(t,arguments)}var a=i(e);return r}var i=e("./createCtorWrapper");t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./createCtorWrapper":101}],101:[function(e,t,n){function r(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=i(e.prototype),r=e.apply(n,t);return a(r)?r:n}}var i=e("./baseCreate"),a=e("../lang/isObject");t.exports=r},{"../lang/isObject":144,"./baseCreate":70}],102:[function(e,t,n){function r(e,t){return function(n,r,l){if(r=i(r,l,3),s(n)){var u=o(n,r,t);return u>-1?n[u]:void 0}return a(n,r,e)}}var i=e("./baseCallback"),a=e("./baseFind"),o=e("./baseFindIndex"),s=e("../lang/isArray");t.exports=r},{"../lang/isArray":140,"./baseCallback":67,"./baseFind":72,"./baseFindIndex":73}],103:[function(e,t,n){function r(e,t){return function(n,r,o){return"function"==typeof r&&void 0===o&&a(n)?e(n,r):t(n,i(r,o,3))}}var i=e("./bindCallback"),a=e("../lang/isArray");t.exports=r},{"../lang/isArray":140,"./bindCallback":94}],104:[function(e,t,n){(function(n){function r(e,t,x,j,O,A,S,_,k,E){function C(){for(var d=arguments.length,m=d,y=Array(d);m--;)y[m]=arguments[m];if(j&&(y=a(y,j,O)),A&&(y=o(y,A,S)),$||D){var b=C.placeholder,L=c(y,b);if(d-=L.length,E>d){var R=_?i(_):void 0,N=w(E-d,0),F=$?L:void 0,B=$?void 0:L,q=$?y:void 0,V=$?void 0:y;t|=$?g:v,t&=~($?v:g),U||(t&=~(h|f));var z=[e,t,x,q,F,V,B,R,k,N],H=r.apply(void 0,z);return l(e)&&p(H,z),H.placeholder=b,H}}var J=T?x:this,Y=M?J[e]:e;return _&&(y=u(y,_)),I&&k<y.length&&(y.length=k),this&&this!==n&&this instanceof C&&(Y=P||s(e)),Y.apply(J,y)}var I=t&b,T=t&h,M=t&f,$=t&m,U=t&d,D=t&y,P=M?void 0:s(e);return C}var i=e("./arrayCopy"),a=e("./composeArgs"),o=e("./composeArgsRight"),s=e("./createCtorWrapper"),l=e("./isLaziable"),u=e("./reorder"),c=e("./replaceHolders"),p=e("./setData"),h=1,f=2,d=4,m=8,y=16,g=32,v=64,b=128,w=Math.max;t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./arrayCopy":62,"./composeArgs":96,"./composeArgsRight":97,"./createCtorWrapper":101,"./isLaziable":124,"./reorder":131,"./replaceHolders":132,"./setData":133}],105:[function(e,t,n){(function(n){function r(e,t,r,o){function s(){for(var t=-1,i=arguments.length,a=-1,c=o.length,p=Array(c+i);++a<c;)p[a]=o[a];for(;i--;)p[a++]=arguments[++t];var h=this&&this!==n&&this instanceof s?u:e;return h.apply(l?r:this,p)}var l=t&a,u=i(e);return s}var i=e("./createCtorWrapper"),a=1;t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./createCtorWrapper":101}],106:[function(e,t,n){function r(e,t,n,r,g,v,b,w){var x=t&h;if(!x&&"function"!=typeof e)throw new TypeError(m);var j=r?r.length:0;if(j||(t&=~(f|d),r=g=void 0),j-=g?g.length:0,t&d){var O=r,A=g;r=g=void 0}var S=x?void 0:l(e),_=[e,t,n,r,g,O,A,v,b,w];if(S&&(u(_,S),t=_[1],w=_[9]),_[9]=null==w?x?0:e.length:y(w-j,0)||0,t==p)var k=a(_[0],_[2]);else k=t!=f&&t!=(p|f)||_[4].length?o.apply(void 0,_):s.apply(void 0,_);var E=S?i:c;return E(k,_)}var i=e("./baseSetData"),a=e("./createBindWrapper"),o=e("./createHybridWrapper"),s=e("./createPartialWrapper"),l=e("./getData"),u=e("./mergeData"),c=e("./setData"),p=1,h=2,f=32,d=64,m="Expected a function",y=Math.max;t.exports=r},{"./baseSetData":88,"./createBindWrapper":100,"./createHybridWrapper":104,"./createPartialWrapper":105,"./getData":110,"./mergeData":128,"./setData":133}],107:[function(e,t,n){function r(e,t,n,r,a,o,s){var l=-1,u=e.length,c=t.length;if(u!=c&&!(a&&c>u))return!1;for(;++l<u;){var p=e[l],h=t[l],f=r?r(a?h:p,a?p:h,l):void 0;if(void 0!==f){if(f)continue;return!1}if(a){if(!i(t,function(e){return p===e||n(p,e,r,a,o,s)}))return!1}else if(p!==h&&!n(p,h,r,a,o,s))return!1}return!0}var i=e("./arraySome");t.exports=r},{"./arraySome":65}],108:[function(e,t,n){function r(e,t,n){switch(n){case i:case a:return+e==+t;case o:return e.name==t.name&&e.message==t.message;case s:return e!=+e?t!=+t:e==+t;case l:case u:return e==t+""}return!1}var i="[object Boolean]",a="[object Date]",o="[object Error]",s="[object Number]",l="[object RegExp]",u="[object String]";t.exports=r},{}],109:[function(e,t,n){function r(e,t,n,r,a,s,l){var u=i(e),c=u.length,p=i(t),h=p.length;if(c!=h&&!a)return!1;for(var f=c;f--;){var d=u[f];if(!(a?d in t:o.call(t,d)))return!1}for(var m=a;++f<c;){d=u[f];var y=e[d],g=t[d],v=r?r(a?g:y,a?y:g,d):void 0;if(!(void 0===v?n(y,g,r,a,s,l):v))return!1;m||(m="constructor"==d)}if(!m){var b=e.constructor,w=t.constructor;if(b!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof b&&b instanceof b&&"function"==typeof w&&w instanceof w))return!1}return!0}var i=e("../object/keys"),a=Object.prototype,o=a.hasOwnProperty;t.exports=r},{"../object/keys":149}],110:[function(e,t,n){var r=e("./metaMap"),i=e("../utility/noop"),a=r?function(e){return r.get(e)}:i;t.exports=a},{"../utility/noop":155,"./metaMap":129}],111:[function(e,t,n){function r(e){for(var t=e.name+"",n=i[t],r=n?n.length:0;r--;){var a=n[r],o=a.func;if(null==o||o==e)return a.name}return t}var i=e("./realNames");t.exports=r},{"./realNames":130}],112:[function(e,t,n){var r=e("./baseProperty"),i=r("length");t.exports=i},{"./baseProperty":86}],113:[function(e,t,n){function r(e){for(var t=a(e),n=t.length;n--;)t[n][2]=i(t[n][1]);return t}var i=e("./isStrictComparable"),a=e("../object/pairs");t.exports=r},{"../object/pairs":151,"./isStrictComparable":127}],114:[function(e,t,n){function r(e,t){var n=null==e?void 0:e[t];return i(n)?n:void 0}var i=e("../lang/isNative");t.exports=r},{"../lang/isNative":143}],115:[function(e,t,n){function r(e,t,n){for(var r=e.length,i=t+(n?0:-1);n?i--:++i<r;){var a=e[i];if(a!==a)return i}return-1}t.exports=r},{}],116:[function(e,t,n){function r(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&a.call(e,"index")&&(n.index=e.index,n.input=e.input),n}var i=Object.prototype,a=i.hasOwnProperty;t.exports=r},{}],117:[function(e,t,n){(function(n){function r(e,t,n){var r=e.constructor;switch(t){case c:return i(e);case a:case o:return new r(+e);case p:case h:case f:case d:case m:case y:case g:case v:case b:r instanceof r&&(r=j[t]);var x=e.buffer;return new r(n?i(x):x,e.byteOffset,e.length);case s:case u:return new r(e);case l:var O=new r(e.source,w.exec(e));O.lastIndex=e.lastIndex}return O}var i=e("./bufferClone"),a="[object Boolean]",o="[object Date]",s="[object Number]",l="[object RegExp]",u="[object String]",c="[object ArrayBuffer]",p="[object Float32Array]",h="[object Float64Array]",f="[object Int8Array]",d="[object Int16Array]",m="[object Int32Array]",y="[object Uint8Array]",g="[object Uint8ClampedArray]",v="[object Uint16Array]",b="[object Uint32Array]",w=/\w*$/,x=n.Uint8Array,j={};j[p]=n.Float32Array,j[h]=n.Float64Array,j[f]=n.Int8Array,j[d]=n.Int16Array,j[m]=n.Int32Array,j[y]=x,j[g]=n.Uint8ClampedArray,j[v]=n.Uint16Array,j[b]=n.Uint32Array,t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./bufferClone":95}],118:[function(e,t,n){function r(e){var t=e.constructor;return"function"==typeof t&&t instanceof t||(t=Object),new t}t.exports=r},{}],119:[function(e,t,n){function r(e){return null!=e&&a(i(e))}var i=e("./getLength"),a=e("./isLength");t.exports=r},{"./getLength":112,"./isLength":125}],120:[function(e,t,n){var r=function(){try{Object({toString:0}+"")}catch(e){return function(){return!1}}return function(e){return"function"!=typeof e.toString&&"string"==typeof(e+"")}}();t.exports=r},{}],121:[function(e,t,n){function r(e,t){return e="number"==typeof e||i.test(e)?+e:-1,t=null==t?a:t,e>-1&&e%1==0&&t>e}var i=/^\d+$/,a=9007199254740991;t.exports=r},{}],122:[function(e,t,n){function r(e,t,n){if(!o(n))return!1;var r=typeof t;if("number"==r?i(n)&&a(t,n.length):"string"==r&&t in n){var s=n[t];return e===e?e===s:s!==s}return!1}var i=e("./isArrayLike"),a=e("./isIndex"),o=e("../lang/isObject");t.exports=r},{"../lang/isObject":144,"./isArrayLike":119,"./isIndex":121}],123:[function(e,t,n){function r(e,t){var n=typeof e;if("string"==n&&s.test(e)||"number"==n)return!0;if(i(e))return!1;var r=!o.test(e);return r||null!=t&&e in a(t)}var i=e("../lang/isArray"),a=e("./toObject"),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\n\\]|\\.)*?\1)\]/,s=/^\w*$/;t.exports=r},{"../lang/isArray":140,"./toObject":135}],124:[function(e,t,n){function r(e){var t=o(e),n=s[t];if("function"!=typeof n||!(t in i.prototype))return!1;if(e===n)return!0;var r=a(n);return!!r&&e===r[0]}var i=e("./LazyWrapper"),a=e("./getData"),o=e("./getFuncName"),s=e("../chain/lodash");t.exports=r},{"../chain/lodash":51,"./LazyWrapper":60,"./getData":110,"./getFuncName":111}],125:[function(e,t,n){function r(e){return"number"==typeof e&&e>-1&&e%1==0&&i>=e}var i=9007199254740991;t.exports=r},{}],126:[function(e,t,n){function r(e){return!!e&&"object"==typeof e}t.exports=r},{}],127:[function(e,t,n){function r(e){return e===e&&!i(e)}var i=e("../lang/isObject");t.exports=r},{"../lang/isObject":144}],128:[function(e,t,n){function r(e,t){var n=e[1],r=t[1],m=n|r,y=p>m,g=r==p&&n==c||r==p&&n==h&&e[7].length<=t[8]||r==(p|h)&&n==c;
if(!y&&!g)return e;r&l&&(e[2]=t[2],m|=n&l?0:u);var v=t[3];if(v){var b=e[3];e[3]=b?a(b,v,t[4]):i(v),e[4]=b?s(e[3],f):i(t[4])}return v=t[5],v&&(b=e[5],e[5]=b?o(b,v,t[6]):i(v),e[6]=b?s(e[5],f):i(t[6])),v=t[7],v&&(e[7]=i(v)),r&p&&(e[8]=null==e[8]?t[8]:d(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=m,e}var i=e("./arrayCopy"),a=e("./composeArgs"),o=e("./composeArgsRight"),s=e("./replaceHolders"),l=1,u=4,c=8,p=128,h=256,f="__lodash_placeholder__",d=Math.min;t.exports=r},{"./arrayCopy":62,"./composeArgs":96,"./composeArgsRight":97,"./replaceHolders":132}],129:[function(e,t,n){(function(n){var r=e("./getNative"),i=r(n,"WeakMap"),a=i&&new i;t.exports=a}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./getNative":114}],130:[function(e,t,n){var r={};t.exports=r},{}],131:[function(e,t,n){function r(e,t){for(var n=e.length,r=o(t.length,n),s=i(e);r--;){var l=t[r];e[r]=a(l,n)?s[l]:void 0}return e}var i=e("./arrayCopy"),a=e("./isIndex"),o=Math.min;t.exports=r},{"./arrayCopy":62,"./isIndex":121}],132:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length,a=-1,o=[];++n<r;)e[n]===t&&(e[n]=i,o[++a]=n);return o}var i="__lodash_placeholder__";t.exports=r},{}],133:[function(e,t,n){var r=e("./baseSetData"),i=e("../date/now"),a=150,o=16,s=function(){var e=0,t=0;return function(n,s){var l=i(),u=o-(l-t);if(t=l,u>0){if(++e>=a)return n}else e=0;return r(n,s)}}();t.exports=s},{"../date/now":57,"./baseSetData":88}],134:[function(e,t,n){function r(e){for(var t=u(e),n=t.length,r=n&&e.length,c=!!r&&s(r)&&(a(e)||i(e)||l(e)),h=-1,f=[];++h<n;){var d=t[h];(c&&o(d,r)||p.call(e,d))&&f.push(d)}return f}var i=e("../lang/isArguments"),a=e("../lang/isArray"),o=e("./isIndex"),s=e("./isLength"),l=e("../lang/isString"),u=e("../object/keysIn"),c=Object.prototype,p=c.hasOwnProperty;t.exports=r},{"../lang/isArguments":139,"../lang/isArray":140,"../lang/isString":146,"../object/keysIn":150,"./isIndex":121,"./isLength":125}],135:[function(e,t,n){function r(e){if(o.unindexedChars&&a(e)){for(var t=-1,n=e.length,r=Object(e);++t<n;)r[t]=e.charAt(t);return r}return i(e)?e:Object(e)}var i=e("../lang/isObject"),a=e("../lang/isString"),o=e("../support");t.exports=r},{"../lang/isObject":144,"../lang/isString":146,"../support":153}],136:[function(e,t,n){function r(e){if(a(e))return e;var t=[];return i(e).replace(o,function(e,n,r,i){t.push(r?i.replace(s,"$1"):n||e)}),t}var i=e("./baseToString"),a=e("../lang/isArray"),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\n\\]|\\.)*?)\2)\]/g,s=/\\(\\)?/g;t.exports=r},{"../lang/isArray":140,"./baseToString":90}],137:[function(e,t,n){function r(e){return e instanceof i?e.clone():new a(e.__wrapped__,e.__chain__,o(e.__actions__))}var i=e("./LazyWrapper"),a=e("./LodashWrapper"),o=e("./arrayCopy");t.exports=r},{"./LazyWrapper":60,"./LodashWrapper":61,"./arrayCopy":62}],138:[function(e,t,n){function r(e,t,n){return"function"==typeof t?i(e,!0,a(t,n,3)):i(e,!0)}var i=e("../internal/baseClone"),a=e("../internal/bindCallback");t.exports=r},{"../internal/baseClone":68,"../internal/bindCallback":94}],139:[function(e,t,n){function r(e){return a(e)&&i(e)&&s.call(e,"callee")&&!l.call(e,"callee")}var i=e("../internal/isArrayLike"),a=e("../internal/isObjectLike"),o=Object.prototype,s=o.hasOwnProperty,l=o.propertyIsEnumerable;t.exports=r},{"../internal/isArrayLike":119,"../internal/isObjectLike":126}],140:[function(e,t,n){var r=e("../internal/getNative"),i=e("../internal/isLength"),a=e("../internal/isObjectLike"),o="[object Array]",s=Object.prototype,l=s.toString,u=r(Array,"isArray"),c=u||function(e){return a(e)&&i(e.length)&&l.call(e)==o};t.exports=c},{"../internal/getNative":114,"../internal/isLength":125,"../internal/isObjectLike":126}],141:[function(e,t,n){function r(e){return null==e?!0:o(e)&&(a(e)||u(e)||i(e)||l(e)&&s(e.splice))?!e.length:!c(e).length}var i=e("./isArguments"),a=e("./isArray"),o=e("../internal/isArrayLike"),s=e("./isFunction"),l=e("../internal/isObjectLike"),u=e("./isString"),c=e("../object/keys");t.exports=r},{"../internal/isArrayLike":119,"../internal/isObjectLike":126,"../object/keys":149,"./isArguments":139,"./isArray":140,"./isFunction":142,"./isString":146}],142:[function(e,t,n){function r(e){return i(e)&&s.call(e)==a}var i=e("./isObject"),a="[object Function]",o=Object.prototype,s=o.toString;t.exports=r},{"./isObject":144}],143:[function(e,t,n){function r(e){return null==e?!1:i(e)?p.test(u.call(e)):o(e)&&(a(e)?p:s).test(e)}var i=e("./isFunction"),a=e("../internal/isHostObject"),o=e("../internal/isObjectLike"),s=/^\[object .+?Constructor\]$/,l=Object.prototype,u=Function.prototype.toString,c=l.hasOwnProperty,p=RegExp("^"+u.call(c).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=r},{"../internal/isHostObject":120,"../internal/isObjectLike":126,"./isFunction":142}],144:[function(e,t,n){function r(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}t.exports=r},{}],145:[function(e,t,n){function r(e){var t;if(!s(e)||h.call(e)!=u||o(e)||a(e)||!p.call(e,"constructor")&&(t=e.constructor,"function"==typeof t&&!(t instanceof t)))return!1;var n;return l.ownLast?(i(e,function(e,t,r){return n=p.call(r,t),!1}),n!==!1):(i(e,function(e,t){n=t}),void 0===n||p.call(e,n))}var i=e("../internal/baseForIn"),a=e("./isArguments"),o=e("../internal/isHostObject"),s=e("../internal/isObjectLike"),l=e("../support"),u="[object Object]",c=Object.prototype,p=c.hasOwnProperty,h=c.toString;t.exports=r},{"../internal/baseForIn":75,"../internal/isHostObject":120,"../internal/isObjectLike":126,"../support":153,"./isArguments":139}],146:[function(e,t,n){function r(e){return"string"==typeof e||i(e)&&s.call(e)==a}var i=e("../internal/isObjectLike"),a="[object String]",o=Object.prototype,s=o.toString;t.exports=r},{"../internal/isObjectLike":126}],147:[function(e,t,n){function r(e){return a(e)&&i(e.length)&&!!C[T.call(e)]}var i=e("../internal/isLength"),a=e("../internal/isObjectLike"),o="[object Arguments]",s="[object Array]",l="[object Boolean]",u="[object Date]",c="[object Error]",p="[object Function]",h="[object Map]",f="[object Number]",d="[object Object]",m="[object RegExp]",y="[object Set]",g="[object String]",v="[object WeakMap]",b="[object ArrayBuffer]",w="[object Float32Array]",x="[object Float64Array]",j="[object Int8Array]",O="[object Int16Array]",A="[object Int32Array]",S="[object Uint8Array]",_="[object Uint8ClampedArray]",k="[object Uint16Array]",E="[object Uint32Array]",C={};C[w]=C[x]=C[j]=C[O]=C[A]=C[S]=C[_]=C[k]=C[E]=!0,C[o]=C[s]=C[b]=C[l]=C[u]=C[c]=C[p]=C[h]=C[f]=C[d]=C[m]=C[y]=C[g]=C[v]=!1;var I=Object.prototype,T=I.toString;t.exports=r},{"../internal/isLength":125,"../internal/isObjectLike":126}],148:[function(e,t,n){function r(e){return void 0===e}t.exports=r},{}],149:[function(e,t,n){var r=e("../internal/getNative"),i=e("../internal/isArrayLike"),a=e("../lang/isObject"),o=e("../internal/shimKeys"),s=e("../support"),l=r(Object,"keys"),u=l?function(e){var t=null==e?void 0:e.constructor;return"function"==typeof t&&t.prototype===e||("function"==typeof e?s.enumPrototypes:i(e))?o(e):a(e)?l(e):[]}:o;t.exports=u},{"../internal/getNative":114,"../internal/isArrayLike":119,"../internal/shimKeys":134,"../lang/isObject":144,"../support":153}],150:[function(e,t,n){function r(e){if(null==e)return[];c(e)||(e=Object(e));var t=e.length;t=t&&u(t)&&(o(e)||a(e)||p(e))&&t||0;for(var n=e.constructor,r=-1,i=s(n)&&n.prototype||A,f=i===e,d=Array(t),m=t>0,g=h.enumErrorProps&&(e===O||e instanceof Error),v=h.enumPrototypes&&s(e);++r<t;)d[r]=r+"";for(var w in e)v&&"prototype"==w||g&&("message"==w||"name"==w)||m&&l(w,t)||"constructor"==w&&(f||!_.call(e,w))||d.push(w);if(h.nonEnumShadows&&e!==A){var C=e===S?x:e===O?y:k.call(e),I=E[C]||E[b];for(C==b&&(i=A),t=j.length;t--;){w=j[t];var T=I[w];f&&T||(T?!_.call(e,w):e[w]===i[w])||d.push(w)}}return d}var i=e("../internal/arrayEach"),a=e("../lang/isArguments"),o=e("../lang/isArray"),s=e("../lang/isFunction"),l=e("../internal/isIndex"),u=e("../internal/isLength"),c=e("../lang/isObject"),p=e("../lang/isString"),h=e("../support"),f="[object Array]",d="[object Boolean]",m="[object Date]",y="[object Error]",g="[object Function]",v="[object Number]",b="[object Object]",w="[object RegExp]",x="[object String]",j=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],O=Error.prototype,A=Object.prototype,S=String.prototype,_=A.hasOwnProperty,k=A.toString,E={};E[f]=E[m]=E[v]={constructor:!0,toLocaleString:!0,toString:!0,valueOf:!0},E[d]=E[x]={constructor:!0,toString:!0,valueOf:!0},E[y]=E[g]=E[w]={constructor:!0,toString:!0},E[b]={constructor:!0},i(j,function(e){for(var t in E)if(_.call(E,t)){var n=E[t];n[e]=_.call(n,e)}}),t.exports=r},{"../internal/arrayEach":63,"../internal/isIndex":121,"../internal/isLength":125,"../lang/isArguments":139,"../lang/isArray":140,"../lang/isFunction":142,"../lang/isObject":144,"../lang/isString":146,"../support":153}],151:[function(e,t,n){function r(e){e=a(e);for(var t=-1,n=i(e),r=n.length,o=Array(r);++t<r;){var s=n[t];o[t]=[s,e[s]]}return o}var i=e("./keys"),a=e("../internal/toObject");t.exports=r},{"../internal/toObject":135,"./keys":149}],152:[function(e,t,n){function r(e){return i(e,a(e))}var i=e("../internal/baseValues"),a=e("./keys");t.exports=r},{"../internal/baseValues":91,"./keys":149}],153:[function(e,t,n){var r=Array.prototype,i=Error.prototype,a=Object.prototype,o=a.propertyIsEnumerable,s=r.splice,l={};!function(e){var t=function(){this.x=e},n={0:e,length:e},r=[];t.prototype={valueOf:e,y:e};for(var a in new t)r.push(a);l.enumErrorProps=o.call(i,"message")||o.call(i,"name"),l.enumPrototypes=o.call(t,"prototype"),l.nonEnumShadows=!/valueOf/.test(r),l.ownLast="x"!=r[0],l.spliceObjects=(s.call(n,0,1),!n[0]),l.unindexedChars="x"[0]+Object("x")[0]!="xx"}(1,0),t.exports=l},{}],154:[function(e,t,n){function r(e){return e}t.exports=r},{}],155:[function(e,t,n){function r(){}t.exports=r},{}],156:[function(e,t,n){function r(e){return o(e)?i(e):a(e)}var i=e("../internal/baseProperty"),a=e("../internal/basePropertyDeep"),o=e("../internal/isKey");t.exports=r},{"../internal/baseProperty":86,"../internal/basePropertyDeep":87,"../internal/isKey":123}],157:[function(e,n,r){(function(e){!function(e){"use strict";if("function"==typeof bootstrap)bootstrap("promise",e);else if("object"==typeof r&&"object"==typeof n)n.exports=e();else if("function"==typeof t&&t.amd)t(e);else if("undefined"!=typeof ses){if(!ses.ok())return;ses.makeQ=e}else{if("undefined"==typeof window&&"undefined"==typeof self)throw new Error("This environment was not anticipated by Q. Please file a bug.");var i="undefined"!=typeof window?window:self,a=i.Q;i.Q=e(),i.Q.noConflict=function(){return i.Q=a,this}}}(function(){"use strict";function t(e){return function(){return K.apply(e,arguments)}}function n(e){return e===Object(e)}function r(e){return"[object StopIteration]"===re(e)||e instanceof H}function i(e,t){if(q&&t.stack&&"object"==typeof e&&null!==e&&e.stack&&-1===e.stack.indexOf(ie)){for(var n=[],r=t;r;r=r.source)r.stack&&n.unshift(r.stack);n.unshift(e.stack);var i=n.join("\n"+ie+"\n");e.stack=a(i)}}function a(e){for(var t=e.split("\n"),n=[],r=0;r<t.length;++r){var i=t[r];l(i)||o(i)||!i||n.push(i)}return n.join("\n")}function o(e){return-1!==e.indexOf("(module.js:")||-1!==e.indexOf("(node.js:")}function s(e){var t=/at .+ \((.+):(\d+):(?:\d+)\)$/.exec(e);if(t)return[t[1],Number(t[2])];var n=/at ([^ ]+):(\d+):(?:\d+)$/.exec(e);if(n)return[n[1],Number(n[2])];var r=/.*@(.+):(\d+)$/.exec(e);return r?[r[1],Number(r[2])]:void 0}function l(e){var t=s(e);if(!t)return!1;var n=t[0],r=t[1];return n===z&&r>=J&&ue>=r}function u(){if(q)try{throw new Error}catch(e){var t=e.stack.split("\n"),n=t[0].indexOf("@")>0?t[1]:t[2],r=s(n);if(!r)return;return z=r[0],r[1]}}function c(e,t,n){return function(){return"undefined"!=typeof console&&"function"==typeof console.warn&&console.warn(t+" is deprecated, use "+n+" instead.",new Error("").stack),e.apply(e,arguments)}}function p(e){return e instanceof m?e:b(e)?E(e):k(e)}function h(){function e(e){t=e,a.source=e,X(n,function(t,n){p.nextTick(function(){e.promiseDispatch.apply(e,n)})},void 0),n=void 0,r=void 0}var t,n=[],r=[],i=ee(h.prototype),a=ee(m.prototype);if(a.promiseDispatch=function(e,i,a){var o=W(arguments);n?(n.push(o),"when"===i&&a[1]&&r.push(a[1])):p.nextTick(function(){t.promiseDispatch.apply(t,o)})},a.valueOf=function(){if(n)return a;var e=g(t);return v(e)&&(t=e),e},a.inspect=function(){return t?t.inspect():{state:"pending"}},p.longStackSupport&&q)try{throw new Error}catch(o){a.stack=o.stack.substring(o.stack.indexOf("\n")+1)}return i.promise=a,i.resolve=function(n){t||e(p(n))},i.fulfill=function(n){t||e(k(n))},i.reject=function(n){t||e(_(n))},i.notify=function(e){t||X(r,function(t,n){p.nextTick(function(){n(e)})},void 0)},i}function f(e){if("function"!=typeof e)throw new TypeError("resolver must be a function.");var t=h();try{e(t.resolve,t.reject,t.notify)}catch(n){t.reject(n)}return t.promise}function d(e){return f(function(t,n){for(var r=0,i=e.length;i>r;r++)p(e[r]).then(t,n)})}function m(e,t,n){void 0===t&&(t=function(e){return _(new Error("Promise does not support operation: "+e))}),void 0===n&&(n=function(){return{state:"unknown"}});var r=ee(m.prototype);if(r.promiseDispatch=function(n,i,a){var o;try{o=e[i]?e[i].apply(r,a):t.call(r,i,a)}catch(s){o=_(s)}n&&n(o)},r.inspect=n,n){var i=n();"rejected"===i.state&&(r.exception=i.reason),r.valueOf=function(){var e=n();return"pending"===e.state||"rejected"===e.state?r:e.value}}return r}function y(e,t,n,r){return p(e).then(t,n,r)}function g(e){if(v(e)){var t=e.inspect();if("fulfilled"===t.state)return t.value}return e}function v(e){return e instanceof m}function b(e){return n(e)&&"function"==typeof e.then}function w(e){return v(e)&&"pending"===e.inspect().state}function x(e){return!v(e)||"fulfilled"===e.inspect().state}function j(e){return v(e)&&"rejected"===e.inspect().state}function O(){ae.length=0,oe.length=0,le||(le=!0)}function A(t,n){le&&("object"==typeof e&&"function"==typeof e.emit&&p.nextTick.runAfter(function(){-1!==G(oe,t)&&(e.emit("unhandledRejection",n,t),se.push(t))}),oe.push(t),n&&"undefined"!=typeof n.stack?ae.push(n.stack):ae.push("(no stack) "+n))}function S(t){if(le){var n=G(oe,t);-1!==n&&("object"==typeof e&&"function"==typeof e.emit&&p.nextTick.runAfter(function(){var r=G(se,t);-1!==r&&(e.emit("rejectionHandled",ae[n],t),se.splice(r,1))}),oe.splice(n,1),ae.splice(n,1))}}function _(e){var t=m({when:function(t){return t&&S(this),t?t(e):this}},function(){return this},function(){return{state:"rejected",reason:e}});return A(t,e),t}function k(e){return m({when:function(){return e},get:function(t){return e[t]},set:function(t,n){e[t]=n},"delete":function(t){delete e[t]},post:function(t,n){return null===t||void 0===t?e.apply(void 0,n):e[t].apply(e,n)},apply:function(t,n){return e.apply(t,n)},keys:function(){return ne(e)}},void 0,function(){return{state:"fulfilled",value:e}})}function E(e){var t=h();return p.nextTick(function(){try{e.then(t.resolve,t.reject,t.notify)}catch(n){t.reject(n)}}),t.promise}function C(e){return m({isDef:function(){}},function(t,n){return D(e,t,n)},function(){return p(e).inspect()})}function I(e,t,n){return p(e).spread(t,n)}function T(e){return function(){function t(e,t){var o;if("undefined"==typeof StopIteration){try{o=n[e](t)}catch(s){return _(s)}return o.done?p(o.value):y(o.value,i,a)}try{o=n[e](t)}catch(s){return r(s)?p(s.value):_(s)}return y(o,i,a)}var n=e.apply(this,arguments),i=t.bind(t,"next"),a=t.bind(t,"throw");return i()}}function M(e){p.done(p.async(e)())}function $(e){throw new H(e)}function U(e){return function(){return I([this,P(arguments)],function(t,n){return e.apply(t,n)})}}function D(e,t,n){return p(e).dispatch(t,n)}function P(e){return y(e,function(e){var t=0,n=h();return X(e,function(r,i,a){var o;v(i)&&"fulfilled"===(o=i.inspect()).state?e[a]=o.value:(++t,y(i,function(r){e[a]=r,0===--t&&n.resolve(e)},n.reject,function(e){n.notify({index:a,value:e})}))},void 0),0===t&&n.resolve(e),n.promise})}function L(e){if(0===e.length)return p.resolve();var t=p.defer(),n=0;return X(e,function(r,i,a){function o(e){t.resolve(e)}function s(){n--,0===n&&t.reject(new Error("Can't get fulfillment value from any promise, all promises were rejected."))}function l(e){t.notify({index:a,value:e})}var u=e[a];n++,y(u,o,s,l)},void 0),t.promise}function R(e){return y(e,function(e){return e=Z(e,p),y(P(Z(e,function(e){return y(e,Y,Y)})),function(){return e})})}function N(e){return p(e).allSettled()}function F(e,t){return p(e).then(void 0,void 0,t)}function B(e,t){return p(e).nodeify(t)}var q=!1;try{throw new Error}catch(V){q=!!V.stack}var z,H,J=u(),Y=function(){},Q=function(){function t(){for(var e,t;r.next;)r=r.next,e=r.task,r.task=void 0,t=r.domain,t&&(r.domain=void 0,t.enter()),n(e,t);for(;l.length;)e=l.pop(),n(e);a=!1}function n(e,n){try{e()}catch(r){if(s)throw n&&n.exit(),setTimeout(t,0),n&&n.enter(),r;setTimeout(function(){throw r},0)}n&&n.exit()}var r={task:void 0,next:null},i=r,a=!1,o=void 0,s=!1,l=[];if(Q=function(t){i=i.next={task:t,domain:s&&e.domain,next:null},a||(a=!0,o())},"object"==typeof e&&"[object process]"===e.toString()&&e.nextTick)s=!0,o=function(){e.nextTick(t)};else if("function"==typeof setImmediate)o="undefined"!=typeof window?setImmediate.bind(window,t):function(){setImmediate(t)};else if("undefined"!=typeof MessageChannel){var u=new MessageChannel;u.port1.onmessage=function(){o=c,u.port1.onmessage=t,t()};var c=function(){u.port2.postMessage(0)};o=function(){setTimeout(t,0),c()}}else o=function(){setTimeout(t,0)};return Q.runAfter=function(e){l.push(e),a||(a=!0,o())},Q}(),K=Function.call,W=t(Array.prototype.slice),X=t(Array.prototype.reduce||function(e,t){var n=0,r=this.length;if(1===arguments.length)for(;;){if(n in this){t=this[n++];break}if(++n>=r)throw new TypeError}for(;r>n;n++)n in this&&(t=e(t,this[n],n));return t}),G=t(Array.prototype.indexOf||function(e){for(var t=0;t<this.length;t++)if(this[t]===e)return t;return-1}),Z=t(Array.prototype.map||function(e,t){var n=this,r=[];return X(n,function(i,a,o){r.push(e.call(t,a,o,n))},void 0),r}),ee=Object.create||function(e){function t(){}return t.prototype=e,new t},te=t(Object.prototype.hasOwnProperty),ne=Object.keys||function(e){var t=[];for(var n in e)te(e,n)&&t.push(n);return t},re=t(Object.prototype.toString);H="undefined"!=typeof ReturnValue?ReturnValue:function(e){this.value=e};var ie="From previous event:";p.resolve=p,p.nextTick=Q,p.longStackSupport=!1,"object"==typeof e&&e&&e.env&&e.env.Q_DEBUG&&(p.longStackSupport=!0),p.defer=h,h.prototype.makeNodeResolver=function(){var e=this;return function(t,n){t?e.reject(t):arguments.length>2?e.resolve(W(arguments,1)):e.resolve(n)}},p.Promise=f,p.promise=f,f.race=d,f.all=P,f.reject=_,f.resolve=p,p.passByCopy=function(e){return e},m.prototype.passByCopy=function(){return this},p.join=function(e,t){return p(e).join(t)},m.prototype.join=function(e){return p([this,e]).spread(function(e,t){if(e===t)return e;throw new Error("Can't join: not the same: "+e+" "+t)})},p.race=d,m.prototype.race=function(){return this.then(p.race)},p.makePromise=m,m.prototype.toString=function(){return"[object Promise]"},m.prototype.then=function(e,t,n){function r(t){try{return"function"==typeof e?e(t):t}catch(n){return _(n)}}function a(e){if("function"==typeof t){i(e,s);try{return t(e)}catch(n){return _(n)}}return _(e)}function o(e){return"function"==typeof n?n(e):e}var s=this,l=h(),u=!1;return p.nextTick(function(){s.promiseDispatch(function(e){u||(u=!0,l.resolve(r(e)))},"when",[function(e){u||(u=!0,l.resolve(a(e)))}])}),s.promiseDispatch(void 0,"when",[void 0,function(e){var t,n=!1;try{t=o(e)}catch(r){if(n=!0,!p.onerror)throw r;p.onerror(r)}n||l.notify(t)}]),l.promise},p.tap=function(e,t){return p(e).tap(t)},m.prototype.tap=function(e){return e=p(e),this.then(function(t){return e.fcall(t).thenResolve(t)})},p.when=y,m.prototype.thenResolve=function(e){return this.then(function(){return e})},p.thenResolve=function(e,t){return p(e).thenResolve(t)},m.prototype.thenReject=function(e){return this.then(function(){throw e})},p.thenReject=function(e,t){return p(e).thenReject(t)},p.nearer=g,p.isPromise=v,p.isPromiseAlike=b,p.isPending=w,m.prototype.isPending=function(){return"pending"===this.inspect().state},p.isFulfilled=x,m.prototype.isFulfilled=function(){return"fulfilled"===this.inspect().state},p.isRejected=j,m.prototype.isRejected=function(){return"rejected"===this.inspect().state};var ae=[],oe=[],se=[],le=!0;p.resetUnhandledRejections=O,p.getUnhandledReasons=function(){return ae.slice()},p.stopUnhandledRejectionTracking=function(){O(),le=!1},O(),p.reject=_,p.fulfill=k,p.master=C,p.spread=I,m.prototype.spread=function(e,t){return this.all().then(function(t){return e.apply(void 0,t)},t)},p.async=T,p.spawn=M,p["return"]=$,p.promised=U,p.dispatch=D,m.prototype.dispatch=function(e,t){var n=this,r=h();return p.nextTick(function(){n.promiseDispatch(r.resolve,e,t)}),r.promise},p.get=function(e,t){return p(e).dispatch("get",[t])},m.prototype.get=function(e){return this.dispatch("get",[e])},p.set=function(e,t,n){return p(e).dispatch("set",[t,n])},m.prototype.set=function(e,t){return this.dispatch("set",[e,t])},p.del=p["delete"]=function(e,t){return p(e).dispatch("delete",[t])},m.prototype.del=m.prototype["delete"]=function(e){return this.dispatch("delete",[e])},p.mapply=p.post=function(e,t,n){return p(e).dispatch("post",[t,n])},m.prototype.mapply=m.prototype.post=function(e,t){return this.dispatch("post",[e,t])},p.send=p.mcall=p.invoke=function(e,t){return p(e).dispatch("post",[t,W(arguments,2)])},m.prototype.send=m.prototype.mcall=m.prototype.invoke=function(e){return this.dispatch("post",[e,W(arguments,1)])},p.fapply=function(e,t){return p(e).dispatch("apply",[void 0,t])},m.prototype.fapply=function(e){return this.dispatch("apply",[void 0,e])},p["try"]=p.fcall=function(e){return p(e).dispatch("apply",[void 0,W(arguments,1)])},m.prototype.fcall=function(){return this.dispatch("apply",[void 0,W(arguments)])},p.fbind=function(e){var t=p(e),n=W(arguments,1);return function(){return t.dispatch("apply",[this,n.concat(W(arguments))])}},m.prototype.fbind=function(){var e=this,t=W(arguments);return function(){return e.dispatch("apply",[this,t.concat(W(arguments))])}},p.keys=function(e){return p(e).dispatch("keys",[])},m.prototype.keys=function(){return this.dispatch("keys",[])},p.all=P,m.prototype.all=function(){return P(this)},p.any=L,m.prototype.any=function(){return L(this)},p.allResolved=c(R,"allResolved","allSettled"),m.prototype.allResolved=function(){return R(this)},p.allSettled=N,m.prototype.allSettled=function(){return this.then(function(e){return P(Z(e,function(e){function t(){return e.inspect()}return e=p(e),e.then(t,t)}))})},p.fail=p["catch"]=function(e,t){return p(e).then(void 0,t)},m.prototype.fail=m.prototype["catch"]=function(e){return this.then(void 0,e)},p.progress=F,m.prototype.progress=function(e){return this.then(void 0,void 0,e)},p.fin=p["finally"]=function(e,t){return p(e)["finally"](t)},m.prototype.fin=m.prototype["finally"]=function(e){return e=p(e),this.then(function(t){return e.fcall().then(function(){return t})},function(t){return e.fcall().then(function(){throw t})})},p.done=function(e,t,n,r){return p(e).done(t,n,r)},m.prototype.done=function(t,n,r){var a=function(e){p.nextTick(function(){if(i(e,o),!p.onerror)throw e;p.onerror(e)})},o=t||n||r?this.then(t,n,r):this;"object"==typeof e&&e&&e.domain&&(a=e.domain.bind(a)),o.then(void 0,a)},p.timeout=function(e,t,n){return p(e).timeout(t,n)},m.prototype.timeout=function(e,t){var n=h(),r=setTimeout(function(){t&&"string"!=typeof t||(t=new Error(t||"Timed out after "+e+" ms"),t.code="ETIMEDOUT"),n.reject(t)},e);return this.then(function(e){clearTimeout(r),n.resolve(e)},function(e){clearTimeout(r),n.reject(e)},n.notify),n.promise},p.delay=function(e,t){return void 0===t&&(t=e,e=void 0),p(e).delay(t)},m.prototype.delay=function(e){return this.then(function(t){var n=h();return setTimeout(function(){n.resolve(t)},e),n.promise})},p.nfapply=function(e,t){return p(e).nfapply(t)},m.prototype.nfapply=function(e){var t=h(),n=W(e);return n.push(t.makeNodeResolver()),this.fapply(n).fail(t.reject),t.promise},p.nfcall=function(e){var t=W(arguments,1);return p(e).nfapply(t)},m.prototype.nfcall=function(){var e=W(arguments),t=h();return e.push(t.makeNodeResolver()),this.fapply(e).fail(t.reject),t.promise},p.nfbind=p.denodeify=function(e){var t=W(arguments,1);return function(){var n=t.concat(W(arguments)),r=h();return n.push(r.makeNodeResolver()),p(e).fapply(n).fail(r.reject),r.promise}},m.prototype.nfbind=m.prototype.denodeify=function(){var e=W(arguments);return e.unshift(this),p.denodeify.apply(void 0,e)},p.nbind=function(e,t){var n=W(arguments,2);return function(){function r(){return e.apply(t,arguments)}var i=n.concat(W(arguments)),a=h();return i.push(a.makeNodeResolver()),p(r).fapply(i).fail(a.reject),a.promise}},m.prototype.nbind=function(){var e=W(arguments,0);return e.unshift(this),p.nbind.apply(void 0,e)},p.nmapply=p.npost=function(e,t,n){return p(e).npost(t,n)},m.prototype.nmapply=m.prototype.npost=function(e,t){var n=W(t||[]),r=h();return n.push(r.makeNodeResolver()),this.dispatch("post",[e,n]).fail(r.reject),r.promise},p.nsend=p.nmcall=p.ninvoke=function(e,t){var n=W(arguments,2),r=h();return n.push(r.makeNodeResolver()),p(e).dispatch("post",[t,n]).fail(r.reject),r.promise},m.prototype.nsend=m.prototype.nmcall=m.prototype.ninvoke=function(e){var t=W(arguments,1),n=h();return t.push(n.makeNodeResolver()),this.dispatch("post",[e,t]).fail(n.reject),n.promise},p.nodeify=B,m.prototype.nodeify=function(e){return e?void this.then(function(t){p.nextTick(function(){e(null,t)})},function(t){p.nextTick(function(){e(t)})}):this},p.noConflict=function(){throw new Error("Q.noConflict only works when Q is used as a global")};var ue=u();return p})}).call(this,e("_process"))},{_process:12}],158:[function(e,t,n){function r(){}function i(e){var t={}.toString.call(e);switch(t){case"[object File]":case"[object Blob]":case"[object FormData]":return!0;default:return!1}}function a(e){if(!b(e))return e;var t=[];for(var n in e)null!=e[n]&&o(t,n,e[n]);return t.join("&")}function o(e,t,n){return Array.isArray(n)?n.forEach(function(n){o(e,t,n)}):void e.push(encodeURIComponent(t)+"="+encodeURIComponent(n))}function s(e){for(var t,n,r={},i=e.split("&"),a=0,o=i.length;o>a;++a)n=i[a],t=n.split("="),r[decodeURIComponent(t[0])]=decodeURIComponent(t[1]);return r}function l(e){var t,n,r,i,a=e.split(/\r?\n/),o={};a.pop();for(var s=0,l=a.length;l>s;++s)n=a[s],t=n.indexOf(":"),r=n.slice(0,t).toLowerCase(),i=x(n.slice(t+1)),o[r]=i;return o}function u(e){return/[\/+]json\b/.test(e)}function c(e){return e.split(/ *; */).shift()}function p(e){return g(e.split(/ *; */),function(e,t){var n=t.split(/ *= */),r=n.shift(),i=n.shift();return r&&i&&(e[r]=i),e},{})}function h(e,t){t=t||{},this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!=this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||"undefined"==typeof this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText,this.setStatusProperties(this.xhr.status),this.header=this.headers=l(this.xhr.getAllResponseHeaders()),this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this.setHeaderProperties(this.header),this.body="HEAD"!=this.req.method?this.parseBody(this.text?this.text:this.xhr.response):null}function f(e,t){var n=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",function(){var e=null,t=null;try{t=new h(n)}catch(r){return e=new Error("Parser is unable to parse the response"),e.parse=!0,e.original=r,e.rawResponse=n.xhr&&n.xhr.responseText?n.xhr.responseText:null,e.statusCode=n.xhr&&n.xhr.status?n.xhr.status:null,n.callback(e)}if(n.emit("response",t),e)return n.callback(e,t);if(t.status>=200&&t.status<300)return n.callback(e,t);var i=new Error(t.statusText||"Unsuccessful HTTP response");i.original=e,i.response=t,i.status=t.status,n.callback(i,t)})}function d(e,t){var n=w("DELETE",e);return t&&n.end(t),n}var m,y=e("emitter"),g=e("reduce"),v=e("./request-base"),b=e("./is-object");m="undefined"!=typeof window?window:"undefined"!=typeof self?self:this;var w=t.exports=e("./request").bind(null,f);w.getXHR=function(){if(!(!m.XMLHttpRequest||m.location&&"file:"==m.location.protocol&&m.ActiveXObject))return new XMLHttpRequest;try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){}return!1};var x="".trim?function(e){return e.trim()}:function(e){return e.replace(/(^\s*|\s*$)/g,"")};w.serializeObject=a,w.parseString=s,w.types={html:"text/html",json:"application/json",xml:"application/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},w.serialize={"application/x-www-form-urlencoded":a,"application/json":JSON.stringify},w.parse={"application/x-www-form-urlencoded":s,"application/json":JSON.parse},h.prototype.get=function(e){return this.header[e.toLowerCase()]},h.prototype.setHeaderProperties=function(e){var t=this.header["content-type"]||"";this.type=c(t);var n=p(t);for(var r in n)this[r]=n[r]},h.prototype.parseBody=function(e){var t=w.parse[this.type];return!t&&u(this.type)&&(t=w.parse["application/json"]),t&&e&&(e.length||e instanceof Object)?t(e):null},h.prototype.setStatusProperties=function(e){1223===e&&(e=204);var t=e/100|0;this.status=this.statusCode=e,this.statusType=t,this.info=1==t,this.ok=2==t,this.clientError=4==t,this.serverError=5==t,this.error=4==t||5==t?this.toError():!1,this.accepted=202==e,this.noContent=204==e,this.badRequest=400==e,this.unauthorized=401==e,this.notAcceptable=406==e,this.notFound=404==e,this.forbidden=403==e},h.prototype.toError=function(){var e=this.req,t=e.method,n=e.url,r="cannot "+t+" "+n+" ("+this.status+")",i=new Error(r);return i.status=this.status,i.method=t,i.url=n,i},w.Response=h,y(f.prototype);for(var j in v)f.prototype[j]=v[j];f.prototype.abort=function(){return this.aborted?void 0:(this.aborted=!0,this.xhr.abort(),this.clearTimeout(),this.emit("abort"),this)},f.prototype.type=function(e){return this.set("Content-Type",w.types[e]||e),this},f.prototype.responseType=function(e){return this._responseType=e,this},f.prototype.accept=function(e){return this.set("Accept",w.types[e]||e),this},f.prototype.auth=function(e,t,n){switch(n||(n={type:"basic"}),n.type){case"basic":var r=btoa(e+":"+t);this.set("Authorization","Basic "+r);break;case"auto":this.username=e,this.password=t}return this},f.prototype.query=function(e){return"string"!=typeof e&&(e=a(e)),e&&this._query.push(e),this},f.prototype.attach=function(e,t,n){return this._getFormData().append(e,t,n||t.name),this},f.prototype._getFormData=function(){return this._formData||(this._formData=new m.FormData),this._formData},f.prototype.send=function(e){var t=b(e),n=this._header["content-type"];if(t&&b(this._data))for(var r in e)this._data[r]=e[r];else"string"==typeof e?(n||this.type("form"),n=this._header["content-type"],"application/x-www-form-urlencoded"==n?this._data=this._data?this._data+"&"+e:e:this._data=(this._data||"")+e):this._data=e;return!t||i(e)?this:(n||this.type("json"),this)},h.prototype.parse=function(e){return m.console&&console.warn("Client-side parse() method has been renamed to serialize(). This method is not compatible with superagent v2.0"),this.serialize(e),this},h.prototype.serialize=function(e){return this._parser=e,this},f.prototype.callback=function(e,t){var n=this._callback;this.clearTimeout(),n(e,t)},f.prototype.crossDomainError=function(){var e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");
e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},f.prototype.timeoutError=function(){var e=this._timeout,t=new Error("timeout of "+e+"ms exceeded");t.timeout=e,this.callback(t)},f.prototype.withCredentials=function(){return this._withCredentials=!0,this},f.prototype.end=function(e){var t=this,n=this.xhr=w.getXHR(),a=this._query.join("&"),o=this._timeout,s=this._formData||this._data;this._callback=e||r,n.onreadystatechange=function(){if(4==n.readyState){var e;try{e=n.status}catch(r){e=0}if(0==e){if(t.timedout)return t.timeoutError();if(t.aborted)return;return t.crossDomainError()}t.emit("end")}};var l=function(e){e.total>0&&(e.percent=e.loaded/e.total*100),e.direction="download",t.emit("progress",e)};this.hasListeners("progress")&&(n.onprogress=l);try{n.upload&&this.hasListeners("progress")&&(n.upload.onprogress=l)}catch(c){}if(o&&!this._timer&&(this._timer=setTimeout(function(){t.timedout=!0,t.abort()},o)),a&&(a=w.serializeObject(a),this.url+=~this.url.indexOf("?")?"&"+a:"?"+a),this.username&&this.password?n.open(this.method,this.url,!0,this.username,this.password):n.open(this.method,this.url,!0),this._withCredentials&&(n.withCredentials=!0),"GET"!=this.method&&"HEAD"!=this.method&&"string"!=typeof s&&!i(s)){var p=this._header["content-type"],h=this._parser||w.serialize[p?p.split(";")[0]:""];!h&&u(p)&&(h=w.serialize["application/json"]),h&&(s=h(s))}for(var f in this.header)null!=this.header[f]&&n.setRequestHeader(f,this.header[f]);return this._responseType&&(n.responseType=this._responseType),this.emit("request",this),n.send("undefined"!=typeof s?s:null),this},w.Request=f,w.get=function(e,t,n){var r=w("GET",e);return"function"==typeof t&&(n=t,t=null),t&&r.query(t),n&&r.end(n),r},w.head=function(e,t,n){var r=w("HEAD",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},w.del=d,w["delete"]=d,w.patch=function(e,t,n){var r=w("PATCH",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},w.post=function(e,t,n){var r=w("POST",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},w.put=function(e,t,n){var r=w("PUT",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r}},{"./is-object":159,"./request":161,"./request-base":160,emitter:162,reduce:163}],159:[function(e,t,n){function r(e){return null!=e&&"object"==typeof e}t.exports=r},{}],160:[function(e,t,n){var r=e("./is-object");n.clearTimeout=function(){return this._timeout=0,clearTimeout(this._timer),this},n.parse=function(e){return this._parser=e,this},n.timeout=function(e){return this._timeout=e,this},n.then=function(e,t){return this.end(function(n,r){n?t(n):e(r)})},n.use=function(e){return e(this),this},n.get=function(e){return this._header[e.toLowerCase()]},n.getHeader=n.get,n.set=function(e,t){if(r(e)){for(var n in e)this.set(n,e[n]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this},n.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},n.field=function(e,t){return this._getFormData().append(e,t),this}},{"./is-object":159}],161:[function(e,t,n){function r(e,t,n){return"function"==typeof n?new e("GET",t).end(n):2==arguments.length?new e("GET",t):new e(t,n)}t.exports=r},{}],162:[function(e,t,n){function r(e){return e?i(e):void 0}function i(e){for(var t in r.prototype)e[t]=r.prototype[t];return e}"undefined"!=typeof t&&(t.exports=r),r.prototype.on=r.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},r.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var r,i=0;i<n.length;i++)if(r=n[i],r===t||r.fn===t){n.splice(i,1);break}return this},r.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),n=this._callbacks["$"+e];if(n){n=n.slice(0);for(var r=0,i=n.length;i>r;++r)n[r].apply(this,t)}return this},r.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},r.prototype.hasListeners=function(e){return!!this.listeners(e).length}},{}],163:[function(e,t,n){t.exports=function(e,t,n){for(var r=0,i=e.length,a=3==arguments.length?n:e[r++];i>r;)a=t.call(null,a,e[r],++r,e);return a}},{}]},{},[1])(1)}),window.SwaggerUi=Backbone.Router.extend({dom_id:"swagger_ui",options:null,api:null,headerView:null,mainView:null,initialize:function(e){e=e||{},"model"!==e.defaultModelRendering&&(e.defaultModelRendering="schema"),e.highlightSizeThreshold||(e.highlightSizeThreshold=1e5),e.dom_id&&(this.dom_id=e.dom_id,delete e.dom_id),e.supportedSubmitMethods||(e.supportedSubmitMethods=["get","put","post","delete","head","options","patch"]),"string"==typeof e.oauth2RedirectUrl&&(window.oAuthRedirectUrl=e.oauth2RedirectUrl),$("#"+this.dom_id).length||$("body").append('<div id="'+this.dom_id+'"></div>'),this.options=e,marked.setOptions({gfm:!0});var t=this;this.options.success=function(){return t.render()},this.options.progress=function(e){return t.showMessage(e)},this.options.failure=function(e){return t.onLoadFailure(e)},this.headerView=new SwaggerUi.Views.HeaderView({el:$("#header")}),this.headerView.on("update-swagger-ui",function(e){return t.updateSwaggerUi(e)}),JSONEditor.defaults.iconlibs.swagger=JSONEditor.AbstractIconLib.extend({mapping:{collapse:"collapse",expand:"expand"},icon_prefix:"swagger-"})},setOption:function(e,t){this.options[e]=t},getOption:function(e){return this.options[e]},updateSwaggerUi:function(e){this.options.url=e.url,this.load()},load:function(){this.mainView&&this.mainView.clear(),this.authView&&this.authView.remove();var e=this.options.url;e&&0!==e.indexOf("http")&&(e=this.buildUrl(window.location.href.toString(),e)),this.api&&(this.options.authorizations=this.api.clientAuthorizations.authz),this.options.url=e,this.headerView.update(e),this.api=new SwaggerClient(this.options)},collapseAll:function(){Docs.collapseEndpointListForResource("")},listAll:function(){Docs.collapseOperationsForResource("")},expandAll:function(){Docs.expandOperationsForResource("")},render:function(){var e;switch(this.showMessage("Finished Loading Resource Information. Rendering Swagger UI..."),this.mainView=new SwaggerUi.Views.MainView({model:this.api,el:$("#"+this.dom_id),swaggerOptions:this.options,router:this}).render(),_.isEmpty(this.api.securityDefinitions)||(e=_.map(this.api.securityDefinitions,function(e,t){var n={};return n[t]=e,n}),this.authView=new SwaggerUi.Views.AuthButtonView({data:SwaggerUi.utils.parseSecurityDefinitions(e),router:this}),$("#auth_container").append(this.authView.render().el)),this.showMessage(),this.options.docExpansion){case"full":this.expandAll();break;case"list":this.listAll()}this.renderGFM(),this.options.onComplete&&this.options.onComplete(this.api,this),setTimeout(Docs.shebang.bind(this),100)},buildUrl:function(e,t){if(0===t.indexOf("/")){var n=e.split("/");return e=n[0]+"//"+n[2],e+t}var r=e.length;return e.indexOf("?")>-1&&(r=Math.min(r,e.indexOf("?"))),e.indexOf("#")>-1&&(r=Math.min(r,e.indexOf("#"))),e=e.substring(0,r),-1!==e.indexOf("/",e.length-1)?e+t:e+"/"+t},showMessage:function(e){void 0===e&&(e="");var t=$("#message-bar");t.removeClass("message-fail"),t.addClass("message-success"),t.text(e),window.SwaggerTranslator&&window.SwaggerTranslator.translate(t)},onLoadFailure:function(e){void 0===e&&(e=""),$("#message-bar").removeClass("message-success"),$("#message-bar").addClass("message-fail");var t=$("#message-bar").text(e);return this.options.onFailure&&this.options.onFailure(e),t},renderGFM:function(){$(".markdown").each(function(){$(this).html(marked($(this).html()))}),$(".propDesc",".model-signature .description").each(function(){$(this).html(marked($(this).html())).addClass("markdown")})}}),window.SwaggerUi.Views={},window.SwaggerUi.Models={},window.SwaggerUi.Collections={},window.SwaggerUi.partials={},window.SwaggerUi.utils={},function(){function e(e){"console"in window&&"function"==typeof window.console.warn&&console.warn(e)}window.authorizations={add:function(){if(e("Using window.authorizations is deprecated. Please use SwaggerUi.api.clientAuthorizations.add()."),"undefined"==typeof window.swaggerUi)throw new TypeError("window.swaggerUi is not defined");window.swaggerUi instanceof SwaggerUi&&window.swaggerUi.api.clientAuthorizations.add.apply(window.swaggerUi.api.clientAuthorizations,arguments)}},window.ApiKeyAuthorization=function(){e("window.ApiKeyAuthorization is deprecated. Please use SwaggerClient.ApiKeyAuthorization."),SwaggerClient.ApiKeyAuthorization.apply(window,arguments)},window.PasswordAuthorization=function(){e("window.PasswordAuthorization is deprecated. Please use SwaggerClient.PasswordAuthorization."),SwaggerClient.PasswordAuthorization.apply(window,arguments)}}(),function(e,t){"function"==typeof define&&define.amd?define(["b"],function(n){return e.SwaggerUi=t(n)}):"object"==typeof exports?module.exports=t(require("b")):e.SwaggerUi=t(e.b)}(this,function(){return SwaggerUi}),window.SwaggerUi.utils={parseSecurityDefinitions:function(e){var t=Object.assign({},window.swaggerUi.api.authSchemes||window.swaggerUi.api.securityDefinitions),n=[],r=[],i=[],a=window.SwaggerUi.utils;return Array.isArray(e)?(e.forEach(function(e){var o={},s={};for(var l in e)if(Array.isArray(e[l])){if(!t[l])continue;if(t[l]=t[l]||{},"oauth2"===t[l].type){s[l]=Object.assign({},t[l]),s[l].scopes=Object.assign({},t[l].scopes);for(var u in s[l].scopes)e[l].indexOf(u)<0&&delete s[l].scopes[u];s[l].scopes=a.parseOauth2Scopes(s[l].scopes),i=_.merge(i,s[l].scopes)}else o[l]=Object.assign({},t[l])}else"oauth2"===e[l].type?(s[l]=Object.assign({},e[l]),s[l].scopes=a.parseOauth2Scopes(s[l].scopes),i=_.merge(i,s[l].scopes)):o[l]=e[l];_.isEmpty(o)||r.push(o),_.isEmpty(s)||n.push(s)}),{auths:r,oauth2:n,scopes:i}):null},parseOauth2Scopes:function(e){var t,n=Object.assign({},e),r=[];for(t in n)r.push({scope:t,description:n[t]});return r}},SwaggerUi.Models.ApiKeyAuthModel=Backbone.Model.extend({defaults:{"in":"",name:"",title:"",value:""},initialize:function(){this.on("change",this.validate)},validate:function(){var e=!!this.get("value");return this.set("valid",e),e}}),SwaggerUi.Views.ApiKeyAuthView=Backbone.View.extend({events:{"change .input_apiKey_entry":"apiKeyChange"},selectors:{apikeyInput:".input_apiKey_entry"},template:Handlebars.templates.apikey_auth,initialize:function(e){this.options=e||{},this.router=this.options.router},render:function(){return this.$el.html(this.template(this.model.toJSON())),this},apiKeyChange:function(e){var t=$(e.target).val();t&&this.$(this.selectors.apikeyInput).removeClass("error"),this.model.set("value",t)},isValid:function(){return this.model.validate()},highlightInvalid:function(){this.isValid()||this.$(this.selectors.apikeyInput).addClass("error")}}),SwaggerUi.Views.AuthButtonView=Backbone.View.extend({events:{"click .authorize__btn":"authorizeBtnClick"},tpls:{popup:Handlebars.templates.popup,authBtn:Handlebars.templates.auth_button,authBtnOperation:Handlebars.templates.auth_button_operation},initialize:function(e){this.options=e||{},this.options.data=this.options.data||{},this.isOperation=this.options.isOperation,this.model=this.model||{},this.router=this.options.router,this.auths=this.options.data.oauth2.concat(this.options.data.auths)},render:function(){var e=this.isOperation?"authBtnOperation":"authBtn";return this.$authEl=this.renderAuths(this.auths),this.$el.html(this.tpls[e](this.model)),this},authorizeBtnClick:function(e){var t;e.preventDefault(),t={title:"Available authorizations",content:this.$authEl},this.render(),this.popup=new SwaggerUi.Views.PopupView({model:t}),this.popup.render()},renderAuths:function(e){var t=$("<div>"),n=!1;return e.forEach(function(e){var r=new SwaggerUi.Views.AuthView({data:e,router:this.router}),i=r.render().el;t.append(i),r.isLogout&&(n=!0)},this),this.model.isLogout=n,t}}),SwaggerUi.Collections.AuthsCollection=Backbone.Collection.extend({constructor:function(){var e=Array.prototype.slice.call(arguments);e[0]=this.parse(e[0]),Backbone.Collection.apply(this,e)},add:function(e){var t=Array.prototype.slice.call(arguments);Array.isArray(e)?t[0]=_.map(e,function(e){return this.handleOne(e)},this):t[0]=this.handleOne(e),Backbone.Collection.prototype.add.apply(this,t)},handleOne:function(e){var t=e;if(!(e instanceof Backbone.Model))switch(e.type){case"oauth2":t=new SwaggerUi.Models.Oauth2Model(e);break;case"basic":t=new SwaggerUi.Models.BasicAuthModel(e);break;case"apiKey":t=new SwaggerUi.Models.ApiKeyAuthModel(e);break;default:t=new Backbone.Model(e)}return t},isValid:function(){var e=!0;return this.models.forEach(function(t){t.validate()||(e=!1)}),e},isAuthorized:function(){return this.length===this.where({isLogout:!0}).length},isPartiallyAuthorized:function(){return this.where({isLogout:!0}).length>0},parse:function(e){var t=Object.assign({},window.swaggerUi.api.clientAuthorizations.authz);return _.map(e,function(e,n){var r=t[n]&&"basic"===e.type&&t[n].username&&t[n].password;return _.extend(e,{title:n}),(t[n]||r)&&_.extend(e,{isLogout:!0,value:r?void 0:t[n].value,username:r?t[n].username:void 0,password:r?t[n].password:void 0,valid:!0}),e})}}),SwaggerUi.Views.AuthsCollectionView=Backbone.View.extend({initialize:function(e){this.options=e||{},this.options.data=this.options.data||{},this.router=this.options.router,this.collection=new SwaggerUi.Collections.AuthsCollection(e.data),this.$innerEl=$("<div>"),this.authViews=[]},render:function(){return this.collection.each(function(e){this.renderOneAuth(e)},this),this.$el.html(this.$innerEl.html()?this.$innerEl:""),this},renderOneAuth:function(e){var t,n,r,i=e.get("type");"apiKey"===i?r="ApiKeyAuthView":"basic"===i&&0===this.$innerEl.find(".basic_auth_container").length?r="BasicAuthView":"oauth2"===i&&(r="Oauth2View"),r&&(n=new SwaggerUi.Views[r]({model:e,router:this.router}),t=n.render().el,this.authViews.push(n)),this.$innerEl.append(t)},highlightInvalid:function(){this.authViews.forEach(function(e){e.highlightInvalid()},this)}}),SwaggerUi.Views.AuthView=Backbone.View.extend({events:{"click .auth_submit__button":"authorizeClick","click .auth_logout__button":"logoutClick"},tpls:{main:Handlebars.templates.auth_view},selectors:{innerEl:".auth_inner",authBtn:".auth_submit__button"},initialize:function(e){this.options=e||{},e.data=e.data||{},this.router=this.options.router,this.authsCollectionView=new SwaggerUi.Views.AuthsCollectionView({data:e.data}),this.$el.html(this.tpls.main({isLogout:this.authsCollectionView.collection.isAuthorized(),isAuthorized:this.authsCollectionView.collection.isPartiallyAuthorized()})),this.$innerEl=this.$(this.selectors.innerEl),this.isLogout=this.authsCollectionView.collection.isPartiallyAuthorized()},render:function(){return this.$innerEl.html(this.authsCollectionView.render().el),this},authorizeClick:function(e){e.preventDefault(),e.stopPropagation(),this.authsCollectionView.collection.isValid()?this.authorize():this.authsCollectionView.highlightInvalid()},authorize:function(){this.authsCollectionView.collection.forEach(function(e){var t,n,r=e.get("type");"apiKey"===r?(t=new SwaggerClient.ApiKeyAuthorization(e.get("name"),e.get("value"),e.get("in")),this.router.api.clientAuthorizations.add(e.get("title"),t)):"basic"===r?(n=new SwaggerClient.PasswordAuthorization(e.get("username"),e.get("password")),this.router.api.clientAuthorizations.add(e.get("title"),n)):"oauth2"===r&&this.handleOauth2Login(e)},this),this.router.load()},logoutClick:function(e){e.preventDefault(),this.authsCollectionView.collection.forEach(function(e){window.swaggerUi.api.clientAuthorizations.remove(e.get("title"))}),this.router.load()},handleOauth2Login:function(e){var t,n,r,i=window.location,a=location.pathname.substring(0,location.pathname.lastIndexOf("/")),o=i.protocol+"//"+i.host+a+"/o2c.html",s=window.oAuthRedirectUrl||o,l=null,u=_.map(e.get("scopes"),function(e){return e.scope});window.OAuthSchemeKey=e.get("title"),window.enabledScopes=u;var c=e.get("flow");if("oauth2"!==e.get("type")||!c||"implicit"!==c&&"accessCode"!==c){if("oauth2"===e.get("type")&&c&&"application"===c)return n=e.attributes,window.swaggerUi.tokenName=n.tokenName||"access_token",void this.clientCredentialsFlow(u,n.tokenUrl,window.OAuthSchemeKey);if(e.get("grantTypes")){var p=e.get("grantTypes");for(var h in p)p.hasOwnProperty(h)&&"implicit"===h?(n=p[h],r=n.loginEndpoint.url,l=n.loginEndpoint.url+"?response_type=token",window.swaggerUi.tokenName=n.tokenName):p.hasOwnProperty(h)&&"accessCode"===h&&(n=p[h],r=n.tokenRequestEndpoint.url,l=n.tokenRequestEndpoint.url+"?response_type=code",window.swaggerUi.tokenName=n.tokenName)}}else n=e.attributes,l=n.authorizationUrl+"?response_type="+("implicit"===c?"token":"code"),window.swaggerUi.tokenName=n.tokenName||"access_token",window.swaggerUi.tokenUrl="accessCode"===c?n.tokenUrl:null,t=window.OAuthSchemeKey;redirect_uri=s,l+="&redirect_uri="+encodeURIComponent(s),l+="&realm="+encodeURIComponent(realm),l+="&client_id="+encodeURIComponent(clientId),l+="&scope="+encodeURIComponent(u.join(scopeSeparator)),l+="&state="+encodeURIComponent(t);for(var f in additionalQueryStringParams)l+="&"+f+"="+encodeURIComponent(additionalQueryStringParams[f]);window.open(l)},clientCredentialsFlow:function(e,t,n){var r={client_id:clientId,client_secret:clientSecret,scope:e.join(" "),grant_type:"client_credentials"};$.ajax({url:t,type:"POST",data:r,success:function(e){onOAuthComplete(e,n)},error:function(){onOAuthComplete("")}})}}),SwaggerUi.Models.BasicAuthModel=Backbone.Model.extend({defaults:{username:"",password:"",title:"basic"},initialize:function(){this.on("change",this.validate)},validate:function(){var e=!!this.get("password")&&!!this.get("username");return this.set("valid",e),e}}),SwaggerUi.Views.BasicAuthView=Backbone.View.extend({initialize:function(e){this.options=e||{},this.router=this.options.router},events:{"change .auth_input":"inputChange"},selectors:{usernameInput:".basic_auth__username",passwordInput:".basic_auth__password"},cls:{error:"error"},template:Handlebars.templates.basic_auth,render:function(){return $(this.el).html(this.template(this.model.toJSON())),this},inputChange:function(e){var t=$(e.target),n=t.val(),r=t.prop("name");n&&t.removeClass(this.cls.error),this.model.set(r,n)},isValid:function(){return this.model.validate()},highlightInvalid:function(){this.model.get("username")||this.$(this.selectors.usernameInput).addClass(this.cls.error)}}),SwaggerUi.Views.ContentTypeView=Backbone.View.extend({initialize:function(){},render:function(){return this.model.contentTypeId="ct"+Math.random(),$(this.el).html(Handlebars.templates.content_type(this.model)),this}}),SwaggerUi.Views.HeaderView=Backbone.View.extend({events:{"click #show-pet-store-icon":"showPetStore","click #explore":"showCustom","keyup #input_baseUrl":"showCustomOnKeyup","keyup #input_apiKey":"showCustomOnKeyup"},initialize:function(){},showPetStore:function(){this.trigger("update-swagger-ui",{url:"http://petstore.swagger.io/v2/swagger.json"})},showCustomOnKeyup:function(e){13===e.keyCode&&this.showCustom()},showCustom:function(e){e&&e.preventDefault(),this.trigger("update-swagger-ui",{url:$("#input_baseUrl").val()})},update:function(e,t,n){void 0===n&&(n=!1),$("#input_baseUrl").val(e),n&&this.trigger("update-swagger-ui",{url:e})}}),SwaggerUi.Views.MainView=Backbone.View.extend({apisSorter:{alpha:function(e,t){return e.name.localeCompare(t.name)}},operationsSorters:{alpha:function(e,t){return e.path.localeCompare(t.path)},method:function(e,t){return e.method.localeCompare(t.method)}},initialize:function(e){var t,n,r,i;if(e=e||{},this.router=e.router,e.swaggerOptions.apisSorter&&(t=e.swaggerOptions.apisSorter,n=_.isFunction(t)?t:this.apisSorter[t],_.isFunction(n)&&this.model.apisArray.sort(n)),e.swaggerOptions.operationsSorter&&(t=e.swaggerOptions.operationsSorter,n=_.isFunction(t)?t:this.operationsSorters[t],_.isFunction(n)))for(r in this.model.apisArray)this.model.apisArray[r].operationsArray.sort(n);this.model.auths=[];for(r in this.model.securityDefinitions)i=this.model.securityDefinitions[r],this.model.auths.push({name:r,type:i.type,value:i});"validatorUrl"in e.swaggerOptions?this.model.validatorUrl=e.swaggerOptions.validatorUrl:this.model.url.indexOf("localhost")>0||this.model.url.indexOf("127.0.0.1")>0?this.model.validatorUrl=null:"https:"===window.location.protocol?this.model.validatorUrl="https://online.swagger.io/validator":this.model.validatorUrl="http://online.swagger.io/validator";var a;for(a in this.model.definitions)this.model.definitions[a].type||(this.model.definitions[a].type="object")},render:function(){$(this.el).html(Handlebars.templates.main(this.model)),this.info=this.$(".info")[0],this.info&&this.info.addEventListener("click",this.onLinkClick,!0),this.model.securityDefinitions=this.model.securityDefinitions||{};for(var e={},t=0,n=0;n<this.model.apisArray.length;n++){for(var r=this.model.apisArray[n],i=r.name;"undefined"!=typeof e[i];)i=i+"_"+t,t+=1;r.id=i,e[i]=r,this.addResource(r,this.model.auths)}return $(".propWrap").hover(function(){$(".optionsWrapper",$(this)).show()},function(){$(".optionsWrapper",$(this)).hide()}),this},addResource:function(e,t){e.id=e.id.replace(/\s/g,"_"),e.definitions=this.model.definitions;var n=new SwaggerUi.Views.ResourceView({model:e,router:this.router,tagName:"li",id:"resource_"+e.id,className:"resource",auths:t,swaggerOptions:this.options.swaggerOptions});$("#resources",this.el).append(n.render().el)},clear:function(){$(this.el).html("")},onLinkClick:function(e){var t=e.target;"A"===t.tagName&&t.href&&!t.target&&(e.preventDefault(),window.open(t.href,"_blank"))}}),SwaggerUi.Models.Oauth2Model=Backbone.Model.extend({defaults:{scopes:{}},initialize:function(){this.on("change",this.validate)},setScopes:function(e,t){var n=_.extend({},this.attributes),r=_.findIndex(n.scopes,function(t){return t.scope===e});n.scopes[r].checked=t,this.set(n),this.validate()},validate:function(){var e=!1,t=this.get("scopes"),n=_.findIndex(t,function(e){return e.checked===!0});return t.length>0&&n>=0&&(e=!0),0===t.length&&(e=!0),this.set("valid",e),e}}),SwaggerUi.Views.Oauth2View=Backbone.View.extend({events:{"change .oauth-scope":"scopeChange"},template:Handlebars.templates.oauth2,render:function(){return this.$el.html(this.template(this.model.toJSON())),this},scopeChange:function(e){var t=$(e.target).prop("checked"),n=$(e.target).data("scope");this.model.setScopes(n,t)}}),SwaggerUi.Views.OperationView=Backbone.View.extend({invocationUrl:null,events:{"submit .sandbox":"submitOperation","click .submit":"submitOperation","click .response_hider":"hideResponse","click .toggleOperation":"toggleOperationContent","mouseenter .api-ic":"mouseEnter","dblclick .curl":"selectText","change [name=responseContentType]":"showSnippet"},initialize:function(e){return e=e||{},this.router=e.router,this.auths=e.auths,this.parentId=this.model.parentId,this.nickname=this.model.nickname,this.model.encodedParentId=encodeURIComponent(this.parentId),e.swaggerOptions&&(this.model.defaultRendering=e.swaggerOptions.defaultModelRendering,e.swaggerOptions.showRequestHeaders&&(this.model.showRequestHeaders=!0)),this},selectText:function(e){var t,n,r=document,i=e.target.firstChild;r.body.createTextRange?(t=document.body.createTextRange(),t.moveToElementText(i),t.select()):window.getSelection&&(n=window.getSelection(),t=document.createRange(),t.selectNodeContents(i),n.removeAllRanges(),n.addRange(t))},mouseEnter:function(e){var t=$(this.el).find(".content"),n=e.pageX,r=e.pageY,i=$(window).scrollLeft(),a=$(window).scrollTop(),o=i+$(window).width(),s=a+$(window).height(),l=t.width(),u=t.height();n+l>o&&(n=o-l),i>n&&(n=i),r+u>s&&(r=s-u),a>r&&(r=a);var c={};c.top=r,c.left=n,t.css(c)},render:function(){var e,t,n,r,i,a,o,s,l,u,c,p,h,f,d,m,y,g,v,b,w,x,j,O,A,S,k,E,C,I,T,M,U,D,P,L,R,N,F,B,q;if(a=jQuery.inArray(this.model.method,this.model.supportedSubmitMethods())>=0,a||(this.model.isReadOnly=!0),this.model.description=this.model.description||this.model.notes,this.model.oauth=null,m=this.model.authorizations||this.model.security)if(Array.isArray(m))for(l=0,u=m.length;u>l;l++){n=m[l];for(s in n)for(e in this.auths)if(t=this.auths[e],s===t.name&&"oauth2"===t.type){this.model.oauth={},this.model.oauth.scopes=[],j=t.value.scopes;for(o in j)R=j[o],M=n[s].indexOf(o),M>=0&&(g={scope:o,description:R},this.model.oauth.scopes.push(g))}}else for(o in m)if(R=m[o],"oauth2"===o)for(null===this.model.oauth&&(this.model.oauth={}),void 0===this.model.oauth.scopes&&(this.model.oauth.scopes=[]),d=0,c=R.length;c>d;d++)g=R[d],this.model.oauth.scopes.push(g);if("undefined"!=typeof this.model.responses){this.model.responseMessages=[],O=this.model.responses;for(r in O)N=O[r],I=null,T=this.model.responses[r].schema,T&&T.$ref&&(I=T.$ref,-1!==I.indexOf("#/definitions/")&&(I=I.replace(/^.*#\/definitions\//,""))),this.model.responseMessages.push({code:r,message:N.description,responseModel:I,headers:N.headers,schema:T})}if("undefined"==typeof this.model.responseMessages&&(this.model.responseMessages=[]),U=null,F=this.model.produces,B=this.contains(F,"xml"),q=B?this.contains(F,"json"):!0,this.model.successResponse){P=this.model.successResponse;for(s in P)N=P[s],this.model.successCode=s,"object"==typeof N&&"function"==typeof N.createJSONSample?(this.model.successDescription=N.description,this.model.headers=this.parseResponseHeaders(N.headers),U={sampleJSON:q?JSON.stringify(SwaggerUi.partials.signature.createJSONSample(N),void 0,2):!1,isParam:!1,sampleXML:B?SwaggerUi.partials.signature.createXMLSample(N.name,N.definition,N.models):!1,signature:SwaggerUi.partials.signature.getModelSignature(N.name,N.definition,N.models,N.modelPropertyMacro)}):U={signature:SwaggerUi.partials.signature.getPrimitiveSignature(N)}}else this.model.responseClassSignature&&"string"!==this.model.responseClassSignature&&(U={sampleJSON:this.model.responseSampleJSON,isParam:!1,signature:this.model.responseClassSignature});for($(this.el).html(Handlebars.templates.operation(this.model)),U?(U.defaultRendering=this.model.defaultRendering,C=new SwaggerUi.Views.SignatureView({model:U,router:this.router,tagName:"div"}),$(".model-signature",$(this.el)).append(C.render().el)):(this.model.responseClassSignature="string",$(".model-signature",$(this.el)).html(this.model.type)),i={isParam:!1},i.consumes=this.model.consumes,i.produces=this.model.produces,A=this.model.parameters,y=0,p=A.length;p>y;y++)b=A[y],L=b.type||b.dataType||"","undefined"==typeof L&&(I=b.schema,I&&I.$ref&&(x=I.$ref,L=0===x.indexOf("#/definitions/")?x.substring("#/definitions/".length):x)),L&&"file"===L.toLowerCase()&&(i.consumes||(i.consumes="multipart/form-data")),b.type=L;for(E=new SwaggerUi.Views.ResponseContentTypeView({model:i,router:this.router}),$(".response-content-type",$(this.el)).append(E.render().el),S=this.model.parameters,v=0,h=S.length;h>v;v++)b=S[v],this.addParameter(b,i.consumes);for(k=this.model.responseMessages,w=0,f=k.length;f>w;w++)D=k[w],D.isXML=B,D.isJSON=q,_.isUndefined(D.headers)||(D.headers=this.parseHeadersType(D.headers)),this.addStatusCode(D);if(Array.isArray(this.model.security)){var V=SwaggerUi.utils.parseSecurityDefinitions(this.model.security);V.isLogout=!_.isEmpty(window.swaggerUi.api.clientAuthorizations.authz),this.authView=new SwaggerUi.Views.AuthButtonView({data:V,router:this.router,isOperation:!0,model:{scopes:V.scopes}}),this.$(".authorize-wrapper").append(this.authView.render().el)}return this.showSnippet(),this},parseHeadersType:function(e){var t={string:{"date-time":"dateTime",date:"date"}};return _.forEach(e,function(e){var n;e=e||{},n=t[e.type]&&t[e.type][e.format],_.isUndefined(n)||(e.type=n)}),e},contains:function(e,t){return e.filter(function(e){return e.indexOf(t)>-1?!0:void 0}).length},parseResponseHeaders:function(e){var t="; ",n=_.clone(e);return _.forEach(n,function(e){var n=[];_.forEach(e,function(e,t){var r=["type","description"];-1===r.indexOf(t.toLowerCase())&&n.push(t+": "+e)}),n.join(t),e.other=n}),n},addParameter:function(e,t){e.consumes=t,e.defaultRendering=this.model.defaultRendering,e.schema&&($.extend(!0,e.schema,this.model.definitions[e.type]),e.schema.definitions=this.model.definitions,e.schema.type||(e.schema.type="object"),e.schema.title||(e.schema.title=" "));var n=new SwaggerUi.Views.ParameterView({model:e,tagName:"tr",readOnly:this.model.isReadOnly,swaggerOptions:this.options.swaggerOptions});$(".operation-params",$(this.el)).append(n.render().el)},addStatusCode:function(e){e.defaultRendering=this.model.defaultRendering;var t=new SwaggerUi.Views.StatusCodeView({model:e,tagName:"tr",router:this.router});$(".operation-status",$(this.el)).append(t.render().el)},submitOperation:function(e){var t,n,r,i,a;if(null!==e&&e.preventDefault(),n=$(".sandbox",$(this.el)),t=!0,n.find("input.required").each(function(){$(this).removeClass("error"),""===jQuery.trim($(this).val())&&($(this).addClass("error"),$(this).wiggle({callback:function(e){return function(){$(e).focus()}}(this)}),t=!1)}),n.find("textarea.required:visible").each(function(){$(this).removeClass("error"),""===jQuery.trim($(this).val())&&($(this).addClass("error"),$(this).wiggle({callback:function(e){return function(){return $(e).focus()}}(this)}),t=!1)}),n.find("select.required").each(function(){$(this).removeClass("error"),-1===this.selectedIndex&&($(this).addClass("error"),$(this).wiggle({callback:function(e){return function(){$(e).focus()}}(this)}),t=!1)}),t){if(i=this.getInputMap(n),r=this.isFileUpload(n),a={parent:this},this.options.swaggerOptions)for(var o in this.options.swaggerOptions)a[o]=this.options.swaggerOptions[o];var s;for(s=0;s<this.model.parameters.length;s++){var l=this.model.parameters[s];if(l.jsonEditor&&l.jsonEditor.isEnabled()){var u=l.jsonEditor.getValue();i[l.name]=JSON.stringify(u)}}return a.responseContentType=$("div select[name=responseContentType]",$(this.el)).val(),a.requestContentType=$("div select[name=parameterContentType]",$(this.el)).val(),$(".response_throbber",$(this.el)).show(),r?($(".request_url",$(this.el)).html("<pre></pre>"),$(".request_url pre",$(this.el)).text(this.invocationUrl),a.useJQuery=!0,i.parameterContentType="multipart/form-data",this.map=i,this.model.execute(i,a,this.showCompleteStatus,this.showErrorStatus,this)):(this.map=i,this.model.execute(i,a,this.showCompleteStatus,this.showErrorStatus,this))}},getInputMap:function(e){var t,n,r,i,a,o,s,l,u,c,p,h;for(t={},n=e.find("input"),r=0,i=n.length;i>r;r++)a=n[r],null!==a.value&&jQuery.trim(a.value).length>0&&(t[a.name]=a.value),"file"===a.type&&(t[a.name]=a.files[0]);for(o=e.find("textarea"),s=0,l=o.length;l>s;s++)a=o[s],u=this.getTextAreaValue(a),null!==u&&jQuery.trim(u).length>0&&(t[a.name]=u);for(c=e.find("select"),p=0,h=c.length;h>p;p++)a=c[p],u=this.getSelectedValue(a),null!==u&&jQuery.trim(u).length>0&&(t[a.name]=u);return t},isFileUpload:function(e){var t,n,r,i,a=!1;for(t=e.find("input"),n=0,r=t.length;r>n;n++)i=t[n],"file"===i.type&&(a=!0);return a},success:function(e,t){t.showCompleteStatus(e)},wrap:function(e){var t,n,r,i,a,o,s;for(r={},n=e.getAllResponseHeaders().split("\r"),a=0,o=n.length;o>a;a++)i=n[a],t=i.match(/^([^:]*?):(.*)$/),t||(t=[]),t.shift(),void 0!==t[0]&&void 0!==t[1]&&(r[t[0].trim()]=t[1].trim());return s={},s.content={},s.content.data=e.responseText,s.headers=r,s.request={},s.request.url=this.invocationUrl,s.status=e.status,s},getSelectedValue:function(e){if(e.multiple){for(var t=[],n=0,r=e.options.length;r>n;n++){var i=e.options[n];i.selected&&t.push(i.value)}return t.length>0?t:null}return e.value},hideResponse:function(e){e&&e.preventDefault(),$(".response",$(this.el)).slideUp(),$(".response_hider",$(this.el)).fadeOut();
},showResponse:function(e){var t=JSON.stringify(e,null,"	").replace(/\n/g,"<br>");$(".response_body",$(this.el)).html(_.escape(t))},showErrorStatus:function(e,t){t.showStatus(e)},showCompleteStatus:function(e,t){t.showStatus(e)},formatXml:function(e){var t,n,r,i,a,o,s,l,u,c,p,h,f;for(p=/(>)(<)(\/*)/g,f=/[ ]*(.*)[ ]+\n/g,t=/(<.+>)(.+\n)/g,e=e.replace(/\r\n/g,"\n").replace(p,"$1\n$2$3").replace(f,"$1\n").replace(t,"$1\n$2"),c=0,r="",l=e.split("\n"),i=0,o="other",h={"single->single":0,"single->closing":-1,"single->opening":0,"single->other":0,"closing->single":0,"closing->closing":-1,"closing->opening":0,"closing->other":0,"opening->single":1,"opening->closing":0,"opening->opening":1,"opening->other":1,"other->single":0,"other->closing":-1,"other->opening":0,"other->other":0},n=function(e){var t,n,a,s,l,u,c;u={single:Boolean(e.match(/<.+\/>/)),closing:Boolean(e.match(/<\/.+>/)),opening:Boolean(e.match(/<[^!?].*>/))},l=function(){var e;e=[];for(a in u)c=u[a],c&&e.push(a);return e}()[0],l=void 0===l?"other":l,t=o+"->"+l,o=l,s="",i+=h[t],s=function(){var e,t,r;for(r=[],n=e=0,t=i;t>=0?t>e:e>t;n=t>=0?++e:--e)r.push("  ");return r}().join(""),"opening->closing"===t?r=r.substr(0,r.length-1)+e+"\n":r+=s+e+"\n"},a=0,s=l.length;s>a;a++)u=l[a],n(u);return r},showStatus:function(e){var t,n;void 0===e.content?(n=e.data,t=e.url):(n=e.content.data,t=e.request.url);var r=e.headers;n=jQuery.trim(n);var i=null;r&&(i=r["Content-Type"]||r["content-type"],i&&(i=i.split(";")[0].trim())),$(".response_body",$(this.el)).removeClass("json"),$(".response_body",$(this.el)).removeClass("xml");var a,o,s=function(e){var t=document.createElement("audio");return!(!t.canPlayType||!t.canPlayType(e).replace(/no/,""))};if(n)if("application/json"===i||/\+json$/.test(i)){var l=null;try{l=JSON.stringify(JSON.parse(n),null,"  ")}catch(u){l="can't parse JSON.  Raw result:\n\n"+n}o=$("<code />").text(l),a=$('<pre class="json" />').append(o)}else if("application/xml"===i||/\+xml$/.test(i))o=$("<code />").text(this.formatXml(n)),a=$('<pre class="xml" />').append(o);else if("text/html"===i)o=$("<code />").html(_.escape(n)),a=$('<pre class="xml" />').append(o);else if(/text\/plain/.test(i))o=$("<code />").text(n),a=$('<pre class="plain" />').append(o);else if(/^image\//.test(i))a=$("<img>").attr("src",t);else if(/^audio\//.test(i)&&s(i))a=$("<audio controls>").append($("<source>").attr("src",t).attr("type",i));else if(r["Content-Disposition"]&&/attachment/.test(r["Content-Disposition"])||r["content-disposition"]&&/attachment/.test(r["content-disposition"])||r["Content-Description"]&&/File Transfer/.test(r["Content-Description"])||r["content-description"]&&/File Transfer/.test(r["content-description"]))if("Blob"in window){var c=i||"text/html",p=new Blob([n],{type:c}),h=document.createElement("a"),f=window.URL.createObjectURL(p),d=e.url.substr(e.url.lastIndexOf("/")+1),m=[c,d,f].join(":"),y=r["content-disposition"]||r["Content-Disposition"];if("undefined"!=typeof y){var g=/filename=([^;]*);?/.exec(y);null!==g&&g.length>1&&(m=g[1])}h.setAttribute("href",f),h.setAttribute("download",m),h.innerText="Download "+d,a=$("<div/>").append(h)}else a=$('<pre class="json" />').append("Download headers detected but your browser does not support downloading binary via XHR (Blob).");else r.location||r.Location?window.location=e.url:(o=$("<code />").text(n),a=$('<pre class="json" />').append(o));else o=$("<code />").text("no content"),a=$('<pre class="json" />').append(o);var v=a;$(".request_url",$(this.el)).html("<pre></pre>"),$(".request_url pre",$(this.el)).text(t),$(".response_code",$(this.el)).html("<pre>"+e.status+"</pre>"),$(".response_body",$(this.el)).html(v),$(".response_headers",$(this.el)).html("<pre>"+_.escape(JSON.stringify(e.headers,null,"  ")).replace(/\n/g,"<br>")+"</pre>"),$(".response",$(this.el)).slideDown(),$(".response_hider",$(this.el)).show(),$(".response_throbber",$(this.el)).hide();var b=this.model.asCurl(this.map,{responseContentType:i});b=b.replace("!","&#33;"),$("div.curl",$(this.el)).html("<pre>"+_.escape(b)+"</pre>");var w=this.options.swaggerOptions;if(w.showRequestHeaders){var x=$(".sandbox",$(this.el)),j=this.getInputMap(x),O=this.model.getHeaderParams(j);delete O["Content-Type"],$(".request_headers",$(this.el)).html("<pre>"+_.escape(JSON.stringify(O,null,"  ")).replace(/\n/g,"<br>")+"</pre>")}var A=$(".response_body",$(this.el))[0];return w.highlightSizeThreshold&&"undefined"!=typeof e.data&&e.data.length>w.highlightSizeThreshold?A:hljs.highlightBlock(A)},toggleOperationContent:function(e){var t=$("#"+Docs.escapeResourceName(this.parentId+"_"+this.nickname+"_content"));t.is(":visible")?($.bbq.pushState("#/",2),e.preventDefault(),Docs.collapseOperation(t)):Docs.expandOperation(t)},getTextAreaValue:function(e){var t,n,r,i;if(null===e.value||0===jQuery.trim(e.value).length)return null;if(t=this.getParamByName(e.name),t&&t.type&&"array"===t.type.toLowerCase()){for(n=e.value.split("\n"),r=[],i=0;i<n.length;i++)null!==n[i]&&jQuery.trim(n[i]).length>0&&r.push(n[i]);return r.length>0?r:null}return e.value},showSnippet:function(){var e,t=this.$("[name=responseContentType]"),n=this.$(".operation-status .snippet_xml, .response-class .snippet_xml"),r=this.$(".operation-status .snippet_json, .response-class .snippet_json");t.length&&(e=t.val(),e.indexOf("xml")>-1?(n.show(),r.hide()):(r.show(),n.hide()))},getParamByName:function(e){var t;if(this.model.parameters)for(t=0;t<this.model.parameters.length;t++)if(this.model.parameters[t].name===e)return this.model.parameters[t];return null}}),SwaggerUi.Views.ParameterContentTypeView=Backbone.View.extend({initialize:function(){},render:function(){return this.model.parameterContentTypeId="pct"+Math.random(),$(this.el).html(Handlebars.templates.parameter_content_type(this.model)),this}}),SwaggerUi.Views.ParameterView=Backbone.View.extend({events:{"change [name=parameterContentType]":"toggleParameterSnippet"},initialize:function(){Handlebars.registerHelper("isArray",function(e,t){var n=e.type&&e.type.toLowerCase();return"array"===n||e.allowMultiple?t.fn(this):t.inverse(this)})},render:function(){var e,t,n=this.model.type||this.model.dataType,r=this.model.modelSignature.type,i=this.model.modelSignature.definitions,a=this.model.schema||{},o=this.model.consumes||[];if("undefined"==typeof n&&a.$ref){var s=a.$ref;n=0===s.indexOf("#/definitions/")?s.substring("#/definitions/".length):s}this.model.type=n,this.model.paramType=this.model["in"]||this.model.paramType,this.model.isBody="body"===this.model.paramType||"body"===this.model["in"],this.model.isFile=n&&"file"===n.toLowerCase(),"undefined"==typeof this.model["default"]&&(this.model["default"]=this.model.defaultValue),this.model.hasDefault="undefined"!=typeof this.model["default"],this.model.valueId="m"+this.model.name+Math.random(),this.model.allowableValues&&(this.model.isList=!0);var l=this.contains(o,"xml"),u=l?this.contains(o,"json"):!0;e=SwaggerUi.partials.signature.createParameterJSONSample(r,i);var c=this.template();$(this.el).html(c(this.model));var p={sampleJSON:u?e:!1,sampleXML:e&&l?SwaggerUi.partials.signature.createXMLSample("",a,i,!0):!1,isParam:!0,signature:SwaggerUi.partials.signature.getParameterModelSignature(r,i),defaultRendering:this.model.defaultRendering};e?(t=new SwaggerUi.Views.SignatureView({model:p,tagName:"div"}),$(".model-signature",$(this.el)).append(t.render().el)):$(".model-signature",$(this.el)).html(this.model.signature);var h=!1;if(this.options.swaggerOptions.jsonEditor&&this.model.isBody&&this.model.schema){var f=$(this.el);this.model.jsonEditor=new JSONEditor($(".editor_holder",f)[0],{schema:this.model.schema,startval:this.model["default"],ajax:!0,disable_properties:!0,disable_edit_json:!0,iconlib:"swagger"}),p.jsonEditor=this.model.jsonEditor,$(".body-textarea",f).hide(),$(".editor_holder",f).show(),$(".parameter-content-type",f).change(function(e){"application/xml"===e.target.value?($(".body-textarea",f).show(),$(".editor_holder",f).hide(),this.model.jsonEditor.disable()):($(".body-textarea",f).hide(),$(".editor_holder",f).show(),this.model.jsonEditor.enable())})}this.model.isBody&&(h=!0);var d={isParam:h};if(d.consumes=this.model.consumes,h){var m=new SwaggerUi.Views.ParameterContentTypeView({model:d});$(".parameter-content-type",$(this.el)).append(m.render().el),this.toggleParameterSnippet()}else{var y=new SwaggerUi.Views.ResponseContentTypeView({model:d});$(".response-content-type",$(this.el)).append(y.render().el),this.toggleResponseSnippet()}return this},contains:function(e,t){return e.filter(function(e){return e.indexOf(t)>-1?!0:void 0}).length},toggleParameterSnippet:function(){var e=this.$("[name=parameterContentType]").val();this.toggleSnippet(e)},toggleResponseSnippet:function(){var e=this.$("[name=responseContentType]");e.length&&this.toggleSnippet(e.val())},toggleSnippet:function(e){e=e||"",e.indexOf("xml")>-1?(this.$(".snippet_xml").show(),this.$(".snippet_json").hide()):(this.$(".snippet_json").show(),this.$(".snippet_xml").hide())},template:function(){return this.model.isList?Handlebars.templates.param_list:this.options.readOnly?this.model.required?Handlebars.templates.param_readonly_required:Handlebars.templates.param_readonly:this.model.required?Handlebars.templates.param_required:Handlebars.templates.param}}),SwaggerUi.partials.signature=function(){function e(e){var t,i=e.name,a=e.definition,o=e.config,s=e.models,l=e.config.isParam,u=[],c=a.properties,p=a.additionalProperties,h=a.xml,f=b(h);return f&&u.push(f),c||p?(c=c||{},t=_.map(c,function(e,t){var n,i;return l&&e.readOnly?"":(n=e.xml||{},i=r(t,e,s,o),n.attribute?(u.push(i),""):i)}).join(""),p&&(t+="<!-- additional elements allowed -->"),g(i,t,u)):n()}function t(e,t){return g(e,"<!-- Infinite loop $ref:"+t+" -->")}function n(e){return e=e?": "+e:"","<!-- invalid XML"+e+" -->"}function r(r,i,s,l){var u,c,p=_.isObject(i)?i.$ref:null;l=l||{},l.modelsToIgnore=l.modelsToIgnore||[];var h=_.isString(p)?a(p,r,s,l):o(r,i,s,l);if(!h)return n();switch(h.type){case"array":u=w(h);break;case"object":u=e(h);break;case"loop":u=t(h.name,h.config.loopTo);break;default:u=j(h)}return p&&"loop"!==h.type&&(c=l.modelsToIgnore.indexOf(p),c>-1&&l.modelsToIgnore.splice(c,1)),u}function i(e,t,n,r,i){if(arguments.length<4)throw new Error;this.config=i||{},this.config.modelsToIgnore=this.config.modelsToIgnore||[],this.name=v(e,n.xml),this.definition=n,this.models=r,this.type=t}function a(e,t,n,r){var a=u(e),o=n[a]||{},s=o.definition&&o.definition.type?o.definition.type:"object";return t=t||o.name,r.modelsToIgnore.indexOf(e)>-1?(s="loop",r.loopTo=a):r.modelsToIgnore.push(e),o.definition?new i(t,s,o.definition,n,r):null}function o(e,t,n,r){var a=t.type||"object";return t?new i(e,a,t,n,r):null}function s(e,t,n,i){var a='<?xml version="1.0"?>';return p(a+r(e,t,n,{isParam:i}))}var l=function(e){return _.isPlainObject(e.schema)&&(e=l(e.schema)),e},u=function(e){return"undefined"==typeof e?null:0===e.indexOf("#/definitions/")?e.substring("#/definitions/".length):e},c=function(e){if(/^Inline Model \d+$/.test(e)&&this.inlineModels){var t=parseInt(e.substr("Inline Model".length).trim(),10),n=this.inlineModels[t];return n}return null},p=function(e){var t,n,r,i,a,o,s,l,u,c,p,h,f;for(p=/(>)(<)(\/*)/g,f=/[ ]*(.*)[ ]+\n/g,t=/(<.+>)(.+\n)/g,e=e.replace(p,"$1\n$2$3").replace(f,"$1\n").replace(t,"$1\n$2"),c=0,r="",l=e.split("\n"),i=0,o="other",h={"single->single":0,"single->closing":-1,"single->opening":0,"single->other":0,"closing->single":0,"closing->closing":-1,"closing->opening":0,"closing->other":0,"opening->single":1,"opening->closing":0,"opening->opening":1,"opening->other":1,"other->single":0,"other->closing":-1,"other->opening":0,"other->other":0},n=function(e){var t,n,a,s,l,u,c;u={single:Boolean(e.match(/<.+\/>/)),closing:Boolean(e.match(/<\/.+>/)),opening:Boolean(e.match(/<[^!?].*>/))},l=function(){var e;e=[];for(a in u)c=u[a],c&&e.push(a);return e}()[0],l=void 0===l?"other":l,t=o+"->"+l,o=l,s="",i+=h[t],s=function(){var e,t,r;for(r=[],n=e=0,t=i;t>=0?t>e:e>t;n=t>=0?++e:--e)r.push("  ");return r}().join(""),"opening->closing"===t?r=r.substr(0,r.length-1)+e+"\n":r+=s+e+"\n"},a=0,s=l.length;s>a;a++)u=l[a],n(u);return r},h=function(e,t,n,r){function i(e,t,r){var i,a=t;return e.$ref?(a=e.title||u(e.$ref),i=n[u(e.$ref)]):_.isUndefined(t)&&(a=e.title||"Inline Model "+ ++m,i={definition:e}),r!==!0&&(f[a]=_.isUndefined(i)?{}:i.definition),a}function a(e){var t='<span class="propType">',n=e.type||"object";return e.$ref?t+=i(e,u(e.$ref)):"object"===n?t+=_.isUndefined(e.properties)?"object":i(e):"array"===n?(t+="Array[",_.isArray(e.items)?t+=_.map(e.items,i).join(","):_.isPlainObject(e.items)?t+=_.isUndefined(e.items.$ref)?_.isUndefined(e.items.type)||-1!==_.indexOf(["array","object"],e.items.type)?i(e.items):e.items.type:i(e.items,u(e.items.$ref)):(console.log("Array type's 'items' schema is not an array or an object, cannot process"),t+="object"),t+="]"):t+=e.type,t+="</span>"}function o(e,t){var n="",r=e.type||"object",i="array"===r;switch(_.isUndefined(e.description)||(t+=': <span class="propDesc">'+e.description+"</span>"),e["enum"]&&(t+=' = <span class="propVals">[\''+e["enum"].join("', '")+"']</span>"),i&&(r=_.isPlainObject(e.items)&&!_.isUndefined(e.items.type)?e.items.type:"object"),_.isUndefined(e["default"])||(n+=h("Default",e["default"])),r){case"string":e.minLength&&(n+=h("Min. Length",e.minLength)),e.maxLength&&(n+=h("Max. Length",e.maxLength)),e.pattern&&(n+=h("Reg. Exp.",e.pattern));break;case"integer":case"number":e.minimum&&(n+=h("Min. Value",e.minimum)),e.exclusiveMinimum&&(n+=h("Exclusive Min.","true")),e.maximum&&(n+=h("Max. Value",e.maximum)),e.exclusiveMaximum&&(n+=h("Exclusive Max.","true")),e.multipleOf&&(n+=h("Multiple Of",e.multipleOf))}if(i&&(e.minItems&&(n+=h("Min. Items",e.minItems)),e.maxItems&&(n+=h("Max. Items",e.maxItems)),e.uniqueItems&&(n+=h("Unique Items","true")),e.collectionFormat&&(n+=h("Coll. Format",e.collectionFormat))),_.isUndefined(e.items)&&_.isArray(e["enum"])){var a;a="number"===r||"integer"===r?e["enum"].join(", "):'"'+e["enum"].join('", "')+'"',n+=h("Enum",a)}return n.length>0&&(t='<span class="propWrap">'+t+'<table class="optionsWrapper"><tr><th colspan="2">'+r+"</th></tr>"+n+"</table></span>"),t}function s(e,t){var s,h=e.type||"object",f="array"===e.type,m=c+t+" "+(f?"[":"{")+p;return t&&d.push(t),f?_.isArray(e.items)?m+="<div>"+_.map(e.items,function(e){var t=e.type||"object";return _.isUndefined(e.$ref)?_.indexOf(["array","object"],t)>-1?"object"===t&&_.isUndefined(e.properties)?"object":i(e):o(e,t):i(e,u(e.$ref))}).join(",</div><div>"):_.isPlainObject(e.items)?m+=_.isUndefined(e.items.$ref)?_.indexOf(["array","object"],e.items.type||"object")>-1?(_.isUndefined(e.items.type)||"object"===e.items.type)&&_.isUndefined(e.items.properties)?"<div>object</div>":"<div>"+i(e.items)+"</div>":"<div>"+o(e.items,e.items.type)+"</div>":"<div>"+i(e.items,u(e.items.$ref))+"</div>":(console.log("Array type's 'items' property is not an array or an object, cannot process"),m+="<div>object</div>"):e.$ref?m+="<div>"+i(e,t)+"</div>":"object"===h?(_.isPlainObject(e.properties)&&(s=_.map(e.properties,function(t,i){var s,c=_.indexOf(e.required,i)>=0,p=_.cloneDeep(t),h=c?"required":"",f='<span class="propName '+h+'">'+i+"</span> (";return p["default"]=r(p),p=l(p),_.isUndefined(p.$ref)||(s=n[u(p.$ref)],_.isUndefined(s)||-1!==_.indexOf([void 0,"array","object"],s.definition.type)||(p=l(s.definition))),f+=a(p),c||(f+=', <span class="propOptKey">optional</span>'),t.readOnly&&(f+=', <span class="propReadOnly">read only</span>'),f+=")","<div"+(t.readOnly?' class="readOnly"':"")+">"+o(p,f)}).join(",</div>")),s&&(m+=s+"</div>")):m+="<div>"+o(e,h)+"</div>",m+c+(f?"]":"}")+p}var c='<span class="strong">',p="</span>",h=function(e,t){return'<tr><td class="optionName">'+e+":</td><td>"+t+"</td></tr>"};if(_.isObject(arguments[0])&&(e=void 0,t=arguments[0],n=arguments[1],r=arguments[2]),n=n||{},t=l(t),_.isEmpty(t))return c+"Empty"+p;if("string"==typeof t.$ref&&(e=u(t.$ref),t=n[e],"undefined"==typeof t))return c+e+" is not defined!"+p;"string"!=typeof e&&(e=t.title||"Inline Model"),t.definition&&(t=t.definition),"function"!=typeof r&&(r=function(e){return(e||{})["default"]});for(var f={},d=[],m=0,y=s(t,e);_.keys(f).length>0;)_.forEach(f,function(e,t){var n=_.indexOf(d,t)>-1;delete f[t],n||(d.push(t),y+="<br />"+s(e,t))});return y},f=function(e,t,n,r){e=l(e),"function"!=typeof r&&(r=function(e){return(e||{})["default"]}),n=n||{};var i,a,o=e.type||"object",s=e.format;return _.isUndefined(e.example)?_.isUndefined(e.items)&&_.isArray(e["enum"])&&(a=e["enum"][0]):a=e.example,_.isUndefined(a)&&(e.$ref?(i=t[u(e.$ref)],_.isUndefined(i)||(_.isUndefined(n[i.name])?(n[i.name]=i,a=f(i.definition,t,n,r),delete n[i.name]):a="array"===i.type?[]:{})):_.isUndefined(e["default"])?"string"===o?a="date-time"===s?(new Date).toISOString():"date"===s?(new Date).toISOString().split("T")[0]:"string":"integer"===o?a=0:"number"===o?a=0:"boolean"===o?a=!0:"object"===o?(a={},_.forEach(e.properties,function(e,i){var o=_.cloneDeep(e);o["default"]=r(e),a[i]=f(o,t,n,r)})):"array"===o&&(a=[],_.isArray(e.items)?_.forEach(e.items,function(e){a.push(f(e,t,n,r))}):_.isPlainObject(e.items)?a.push(f(e.items,t,n,r)):_.isUndefined(e.items)?a.push({}):console.log("Array type's 'items' property is not an array or an object, cannot process")):a=e["default"]),a},d=function(e,t){return t=t||{},t[e.name]=e,e.examples&&_.isPlainObject(e.examples)&&e.examples["application/json"]?(e.definition.example=e.examples["application/json"],_.isString(e.definition.example)&&(e.definition.example=jsyaml.safeLoad(e.definition.example))):e.definition.example||(e.definition.example=e.examples),f(e.definition,e.models,t,e.modelPropertyMacro)},m=function(e,t){var n,r;return e instanceof Array&&(r=!0,e=e[0]),"undefined"==typeof e?(e="undefined",n=!0):t[e]?(e=t[e],n=!1):c(e)?(e=c(e),n=!1):n=!0,n?r?"Array["+e+"]":e.toString():r?"Array["+h(e.name,e.definition,e.models,e.modelPropertyMacro)+"]":h(e.name,e.definition,e.models,e.modelPropertyMacro)},y=function(e,t){var n,r,i;if(t=t||{},n=e instanceof Array,i=n?e[0]:e,t[i]?r=d(t[i]):c(i)&&(r=d(c(i))),r){if(r=n?[r]:r,"string"==typeof r)return r;if(_.isObject(r)){var a=r;if(r instanceof Array&&r.length>0&&(a=r[0]),a.nodeName&&"Node"==typeof a){var o=(new XMLSerializer).serializeToString(a);return p(o)}return JSON.stringify(r,null,2)}return r}},g=function(e,t,r){var i,a;return r=r||[],a=r.map(function(e){return" "+e.name+'="'+e.value+'"'}).join(""),e?(i=["<",e,a,">",t,"</",e,">"],i.join("")):n("Node name is not provided")},v=function(e,t){var n=e||"";return t=t||{},t.name&&(n=t.name),t.prefix&&(n=t.prefix+":"+n),n},b=function(e){var t="",n="xmlns";return e=e||{},e.namespace?(t=e.namespace,e.prefix&&(n+=":"+e.prefix),{name:n,value:t}):t},w=function(e){var t,i=e.name,a=e.config,o=e.definition,s=e.models,l=o.items,u=o.xml||{},c=b(u),p=[];return l?(t=r(i,l,s,a),c&&p.push(c),u.wrapped&&(t=g(i,t,p)),t):n()},x=function(e){var t,n;switch(e=e||{},n=e.items||{},t=e.type||""){case"object":return"Object is not a primitive";case"array":return"Array["+(n.format||n.type)+"]";default:return e.format||t}},j=function(e){var t,r=e.name,i=e.definition,a={string:{date:new Date(1).toISOString().split("T")[0],"date-time":new Date(1).toISOString(),"default":"string"},integer:{"default":1},number:{"default":1.1},"boolean":{"default":!0}},o=i.type,s=i.format,l=i.xml||{},u=b(l),c=[];return _.keys(a).indexOf(o)<0?n():(t=_.isArray(i["enum"])?i["enum"][0]:i.example||a[o][s]||a[o]["default"],l.attribute?{name:r,value:t}:(u&&c.push(u),g(r,t,c)))};return{getModelSignature:h,createJSONSample:d,getParameterModelSignature:m,createParameterJSONSample:y,createSchemaXML:r,createXMLSample:s,getPrimitiveSignature:x}}(),SwaggerUi.Views.PopupView=Backbone.View.extend({events:{"click .api-popup-cancel":"cancelClick"},template:Handlebars.templates.popup,className:"api-popup-dialog",selectors:{content:".api-popup-content",main:"#swagger-ui-container"},initialize:function(){this.$el.html(this.template(this.model))},render:function(){return this.$(this.selectors.content).append(this.model.content),$(this.selectors.main).first().append(this.el),this.showPopup(),this},showPopup:function(){this.$el.show()},cancelClick:function(){this.remove()}}),SwaggerUi.Views.ResourceView=Backbone.View.extend({initialize:function(e){e=e||{},this.router=e.router,this.auths=e.auths,""===this.model.description&&(this.model.description=null),this.model.description&&(this.model.summary=this.model.description)},render:function(){var e={};$(this.el).html(Handlebars.templates.resource(this.model));for(var t=0;t<this.model.operationsArray.length;t++){for(var n=this.model.operationsArray[t],r=0,i=n.nickname;"undefined"!=typeof e[i];)i=i+"_"+r,r+=1;e[i]=n,n.nickname=i,n.parentId=this.model.id,n.definitions=this.model.definitions,this.addOperation(n)}return $(".toggleEndpointList",this.el).click(this.callDocs.bind(this,"toggleEndpointListForResource")),$(".collapseResource",this.el).click(this.callDocs.bind(this,"collapseOperationsForResource")),$(".expandResource",this.el).click(this.callDocs.bind(this,"expandOperationsForResource")),this},addOperation:function(e){e.number=this.number;var t=new SwaggerUi.Views.OperationView({model:e,router:this.router,tagName:"li",className:"endpoint",swaggerOptions:this.options.swaggerOptions,auths:this.auths});$(".endpoints",$(this.el)).append(t.render().el),this.number++},callDocs:function(e,t){t.preventDefault(),Docs[e](t.currentTarget.getAttribute("data-id"))}}),SwaggerUi.Views.ResponseContentTypeView=Backbone.View.extend({initialize:function(){},render:function(){return this.model.responseContentTypeId="rct"+Math.random(),$(this.el).html(Handlebars.templates.response_content_type(this.model)),this}}),SwaggerUi.Views.SignatureView=Backbone.View.extend({events:{"click a.description-link":"switchToDescription","click a.snippet-link":"switchToSnippet","mousedown .snippet_json":"jsonSnippetMouseDown","mousedown .snippet_xml":"xmlSnippetMouseDown"},initialize:function(){},render:function(){return $(this.el).html(Handlebars.templates.signature(this.model)),"model"===this.model.defaultRendering?this.switchToDescription():this.switchToSnippet(),this},switchToDescription:function(e){e&&e.preventDefault(),$(".snippet",$(this.el)).hide(),$(".description",$(this.el)).show(),$(".description-link",$(this.el)).addClass("selected"),$(".snippet-link",$(this.el)).removeClass("selected")},switchToSnippet:function(e){e&&e.preventDefault(),$(".snippet",$(this.el)).show(),$(".description",$(this.el)).hide(),$(".snippet-link",$(this.el)).addClass("selected"),$(".description-link",$(this.el)).removeClass("selected")},snippetToTextArea:function(e){var t=$("textarea",$(this.el.parentNode.parentNode.parentNode));""!==$.trim(t.val())&&t.prop("placeholder")!==t.val()||(t.val(e),this.model.jsonEditor&&this.model.jsonEditor.isEnabled()&&this.model.jsonEditor.setValue(JSON.parse(this.model.sampleJSON)))},jsonSnippetMouseDown:function(e){this.model.isParam&&(e&&e.preventDefault(),this.snippetToTextArea(this.model.sampleJSON))},xmlSnippetMouseDown:function(e){this.model.isParam&&(e&&e.preventDefault(),this.snippetToTextArea(this.model.sampleXML))}}),SwaggerUi.Views.StatusCodeView=Backbone.View.extend({initialize:function(e){this.options=e||{},this.router=this.options.router},render:function(){var e,t,n=this.router.api.models[this.model.responseModel];return $(this.el).html(Handlebars.templates.status_code(this.model)),e=this.router.api.models.hasOwnProperty(this.model.responseModel)?{sampleJSON:JSON.stringify(SwaggerUi.partials.signature.createJSONSample(n),void 0,2),sampleXML:this.model.isXML?SwaggerUi.partials.signature.createXMLSample("",this.model.schema,this.router.api.models):!1,isParam:!1,signature:SwaggerUi.partials.signature.getModelSignature(this.model.responseModel,n,this.router.api.models),defaultRendering:this.model.defaultRendering}:{signature:SwaggerUi.partials.signature.getPrimitiveSignature(this.model.schema)},t=new SwaggerUi.Views.SignatureView({model:e,tagName:"div"}),$(".model-signature",this.$el).append(t.render().el),this}})}).call(this);