VS-RL-001 — RateLimiterService + Config + Shutdown

Description
Implement a Redis-backed rate limiting service using rate-limiter-flexible with a shared ioredis client. Provide a reusable helper to create per-policy limiters (read/write will use this in a separate ticket). Ensure fast-fail Redis options, optional soft-timeout wrapper, consistent key prefixing, and graceful shutdown on Sails “lowering”.

Deliverables
- Dependencies installed: rate-limiter-flexible, ioredis.
- /config/volleyStation.js contains:
    - rateLimit: { read: { points: 60, duration: 60 }, write: { points: 20, duration: 60 } }
    - rateLimit knobs: { enableSoftTimeout: true, softTimeoutMs: 75 }
- /api/services/RateLimiterService.js:
    - Singleton ioredis client (from sails.config.connections.redis), base keyPrefix sw:rl:vs.
    - ioredis options: maxRetriesPerRequest=1, enableOfflineQueue=false, connectTimeout≈800ms.
    - Optional soft-timeout wrapper for limiter.consume() governed by config knobs; treat timeout as fail-open.
    - createRateLimiter(policyName, policyCfg, keySelector) that constructs one RateLimiterRedis per policy (execEvenly: true).
    - Logging on exceed/block and Redis errors/timeouts; fail-open on errors/timeouts.
    - Graceful shutdown: close Redis on Sails “lowering” (quit with guard, then disconnect if needed).

Implementation Steps
1) Add dependencies to package.json and install.
2) Create /config/volleyStation.js with read/write limits and soft-timeout knobs.
3) Implement /api/services/RateLimiterService.js:
    - Lazy-init singleton ioredis client with fast-fail options.
    - Implement optional soft-timeout wrapper around limiter.consume() based on config.
    - Implement createRateLimiter(...) returning middleware that uses a single RateLimiterRedis instance per policy.
    - Add logging and fail-open behavior for Redis errors/timeouts.
    - Register Sails “lowering” hook to gracefully close the client.
4) Sanity-check harness (temporary or internal route) to validate fail-open behavior on simulated Redis outage (can be manual).

Acceptance Criteria
- Service can initialize once (singleton) and create reusable policy limiters.
- ioredis connection uses specified fast-fail options.
- Optional soft-timeout wrapper is controlled by config knobs and, when triggered, results in fail-open.
- Graceful shutdown closes Redis client on Sails “lowering” without hanging.
- Fail-open behavior verified (requests proceed; no rate-limit headers; warn logs).
- RateLimiterRedis key prefixing uses sw:rl:vs:<policy>.

Estimate
0.5–1 day


VS-RL-002 — Auth clientId + Policies (read/write) + Wiring + Tests

Description
Augment VolleyStation auth to attach a stable clientId per request, add read/write rate-limit policies using RateLimiterService, wire them to routes, and validate behavior including headers, 429 responses, OPTIONS bypass, and fail-open.

Deliverables
- /api/policies/volley-station/auth.js:
    - After token validation, set req.user.clientId = sha256(Authorization).hex (no raw token logging).
- /api/policies/volley-station/rateLimitRead.js:
    - Middleware from RateLimiterService.createRateLimiter('read', sails.config.volleyStation.rateLimit.read, keySelector).
- /api/policies/volley-station/rateLimitWrite.js:
    - Middleware from RateLimiterService.createRateLimiter('write', sails.config.volleyStation.rateLimit.write, keySelector).
- keySelector for both: (req) => (req.user && req.user.clientId) || req.ip.
- Behavior:
    - Skip OPTIONS (and optionally HEAD).
    - Always set RateLimit-* headers (Limit, Remaining, Reset) on success and on exceed.
    - On exceed: also set Retry-After and return 429 with JSON { error: 'rate_limited', policy, clientId:first8, retryAfterMs }.
    - On Redis error/timeout: warn log; fail-open; do not set rate-limit headers.
- /config/policies.js mappings:
    - Read routes: ['volley-station/auth', 'volley-station/rateLimitRead', 'volley-station/eventAccess'].
    - Write routes: ['volley-station/auth', 'volley-station/rateLimitWrite', 'volley-station/eventAccess'].

Implementation Steps
1) Update auth policy to compute and attach req.user.clientId from Authorization hash.
2) Add rateLimitRead.js and rateLimitWrite.js using RateLimiterService.createRateLimiter with the specified keySelector.
3) Ensure middleware skips OPTIONS (and optionally HEAD).
4) Update /config/policies.js to wire read routes to rateLimitRead and write routes to rateLimitWrite.
5) Manual/automated smoke tests:
    - Read route: observe RateLimit-* headers on allowed calls; exceeding limit returns 429 with Retry-After and JSON body.
    - Write route: same as above.
    - Simulate Redis outage: requests pass (fail-open), no RateLimit-* headers, warn logs present.
    - Verify OPTIONS bypass and that clientId hashing results in consistent identity across calls with the same Authorization.

Acceptance Criteria
- Headers always set on allowed and exceeded requests; Retry-After set on 429.
- 429 JSON payload matches exactly: { error: 'rate_limited', policy, clientId:first8, retryAfterMs }.
- OPTIONS bypass confirmed.
- Fail-open behavior confirmed on Redis error/timeout; no rate-limit headers emitted in fail-open path.
- No raw token logging; clientId derived as sha256(Authorization).hex.

Estimate
0.5–1 day
